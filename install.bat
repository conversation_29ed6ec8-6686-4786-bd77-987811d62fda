@echo off
REM 零信任SAGIN多Agent威胁分析系统 - Windows自动安装脚本

echo ==============================================================================
echo 🛡️  零信任SAGIN多Agent威胁分析系统 - Windows自动安装脚本
echo ==============================================================================
echo.

REM 检查Python
echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装，请先安装Python 3.8+
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

REM 检查虚拟环境
if defined VIRTUAL_ENV (
    echo ✅ 检测到虚拟环境: %VIRTUAL_ENV%
) else (
    echo ⚠️  建议在虚拟环境中安装
    echo    创建虚拟环境: python -m venv sagin-env
    echo    激活环境: sagin-env\Scripts\activate
    echo.
    set /p continue="是否继续安装? (y/N): "
    if /i not "%continue%"=="y" (
        echo 安装已取消
        pause
        exit /b 1
    )
)

REM 升级pip
echo.
echo 📦 升级pip...
python -m pip install --upgrade pip

REM 安装基础依赖
echo.
echo 📦 安装基础依赖 (测试模式)...
pip install numpy pandas torch transformers
if errorlevel 1 (
    echo ❌ 基础依赖安装失败
    pause
    exit /b 1
)

REM 询问是否安装完整依赖
echo.
set /p install_full="是否安装完整依赖 (LLM模式)? (y/N): "
if /i "%install_full%"=="y" (
    echo 📦 安装完整依赖...
    pip install openai sentence-transformers faiss-cpu
)

REM 询问是否安装GPU支持
echo.
set /p install_gpu="是否安装GPU支持? (y/N): "
if /i "%install_gpu%"=="y" (
    echo 📦 安装GPU支持...
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    pip install faiss-gpu
)

REM 验证安装
echo.
echo 🔍 验证安装...
python -c "import numpy, pandas, torch, transformers; print('✅ 基础依赖安装成功')" 2>nul
if errorlevel 1 (
    echo ❌ 基础依赖验证失败
    pause
    exit /b 1
)

python -c "import openai, sentence_transformers, faiss; print('✅ 完整依赖安装成功')" 2>nul
if errorlevel 1 (
    echo ⚠️  部分依赖未安装 (仅影响LLM模式)
)

REM 检查数据文件
echo.
echo 🔍 检查数据文件...
set all_files_exist=true

if exist "output_0515_dataset_fin.json" (
    echo    ✅ output_0515_dataset_fin.json
) else (
    echo    ❌ output_0515_dataset_fin.json (缺失)
    set all_files_exist=false
)

if exist "output_0525_finetune_metrics.json" (
    echo    ✅ output_0525_finetune_metrics.json
) else (
    echo    ❌ output_0525_finetune_metrics.json (缺失)
    set all_files_exist=false
)

if exist "output_1112_strategy_train_data.json" (
    echo    ✅ output_1112_strategy_train_data.json
) else (
    echo    ❌ output_1112_strategy_train_data.json (缺失)
    set all_files_exist=false
)

if "%all_files_exist%"=="true" (
    echo ✅ 所有数据文件检查通过
) else (
    echo ⚠️  部分数据文件缺失，请确保数据文件存在
)

REM 完成安装
echo.
echo ==============================================================================
echo 🎉 安装完成！
echo ==============================================================================
echo.
echo 快速开始:
echo 1. 交互式启动: python quick_start.py
echo 2. 直接运行: python demo_complete_workflow.py --test-mode
echo 3. 查看帮助: python demo_complete_workflow.py --help
echo.
echo 文档参考:
echo - README.md: 快速开始指南
echo - 运行方法说明.md: 详细运行方法
echo.
echo 推荐从测试模式开始体验系统功能！
echo.
pause
