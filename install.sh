#!/bin/bash
# 零信任SAGIN多Agent威胁分析系统 - 自动安装脚本

set -e  # 遇到错误时退出

echo "=============================================================================="
echo "🛡️  零信任SAGIN多Agent威胁分析系统 - 自动安装脚本"
echo "=============================================================================="
echo

# 检查Python版本
echo "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装，请先安装Python 3.8+"
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo "✅ Python版本: $PYTHON_VERSION"

# 检查是否在虚拟环境中
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ 检测到虚拟环境: $VIRTUAL_ENV"
else
    echo "⚠️  建议在虚拟环境中安装"
    echo "   创建虚拟环境: python3 -m venv sagin-env"
    echo "   激活环境: source sagin-env/bin/activate"
    echo
    read -p "是否继续安装? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 1
    fi
fi

# 升级pip
echo
echo "📦 升级pip..."
python3 -m pip install --upgrade pip

# 安装基础依赖
echo
echo "📦 安装基础依赖 (测试模式)..."
pip install numpy pandas torch transformers

# 询问是否安装完整依赖
echo
read -p "是否安装完整依赖 (LLM模式)? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📦 安装完整依赖..."
    pip install openai sentence-transformers faiss-cpu
fi

# 询问是否安装GPU支持
echo
read -p "是否安装GPU支持? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📦 安装GPU支持..."
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    pip install faiss-gpu
fi

# 验证安装
echo
echo "🔍 验证安装..."
python3 -c "
import numpy
import pandas  
import torch
import transformers
print('✅ 基础依赖安装成功')

try:
    import openai
    import sentence_transformers
    import faiss
    print('✅ 完整依赖安装成功')
except ImportError:
    print('⚠️  部分依赖未安装 (仅影响LLM模式)')
"

# 检查数据文件
echo
echo "🔍 检查数据文件..."
required_files=("output_0515_dataset_fin.json" "output_0525_finetune_metrics.json" "output_1112_strategy_train_data.json")
all_files_exist=true

for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file (缺失)"
        all_files_exist=false
    fi
done

if [[ "$all_files_exist" == true ]]; then
    echo "✅ 所有数据文件检查通过"
else
    echo "⚠️  部分数据文件缺失，请确保数据文件存在"
fi

# 完成安装
echo
echo "=============================================================================="
echo "🎉 安装完成！"
echo "=============================================================================="
echo
echo "快速开始:"
echo "1. 交互式启动: python3 quick_start.py"
echo "2. 直接运行: python3 demo_complete_workflow.py --test-mode"
echo "3. 查看帮助: python3 demo_complete_workflow.py --help"
echo
echo "文档参考:"
echo "- README.md: 快速开始指南"
echo "- 运行方法说明.md: 详细运行方法"
echo
echo "推荐从测试模式开始体验系统功能！"
echo
