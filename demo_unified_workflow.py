#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一工作流程演示
展示如何使用统一数据转换器实现协作流程和单独运行的兼容性
"""

import json
import sys
from datetime import datetime
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append('.')

# 导入必要的模块
from data_converter import UnifiedDataConverter

# 导入Agent模块
try:
    from Comprehensive_Decision_Agent import ComprehensiveDecisionAgent, ComprehensiveDecisionConfig
except ImportError:
    import importlib.util
    spec = importlib.util.spec_from_file_location("comprehensive_decision_agent", "Comprehensive Decision Agent.py")
    comprehensive_decision_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(comprehensive_decision_module)
    ComprehensiveDecisionAgent = comprehensive_decision_module.ComprehensiveDecisionAgent
    ComprehensiveDecisionConfig = comprehensive_decision_module.ComprehensiveDecisionConfig

def demo_unified_workflow():
    """演示统一工作流程"""
    
    print("=" * 80)
    print("统一工作流程演示 - Comprehensive Decision Agent")
    print("支持协作流程和单独运行的统一数据格式")
    print("=" * 80)
    
    # 初始化数据转换器
    converter = UnifiedDataConverter()
    
    # 创建Comprehensive Decision Agent配置
    config = ComprehensiveDecisionConfig(
        output_dir="./unified_workflow_outputs",
        max_strategies=10,
        enable_explanation=True,
        decision_threshold=0.5,
        conflict_resolution_method="weighted_average"
    )
    
    # 创建Agent实例
    agent = ComprehensiveDecisionAgent(config)
    
    print("\n🔄 演示场景1: 协作流程模式")
    print("-" * 50)
    
    # 模拟Specific Advice Agent的输出
    specific_advice_output = [
        {
            "Description": "Layer: [ground]\\nNetwork Type: [Network Traffic]\\nThreat Type: [DDoS]\\nTimestamp: [2024-04-13 09:30:17]\\nSource IP: [**********]\\nDestination IP: [*************]\\nProtocol: [TCP]\\nAttack Method: [DDoS LOIT attack]\\nDetails: A DDoS LOIT attack aimed at Ubuntu16 has been identified.",
            "Base Score": 7.4,
            "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"],
            "Threat Type": "DDoS",
            "Advice": "Implement DDoS protection measures, rate limiting, and traffic filtering.",
            "Processing Time": "2024-07-31T10:33:25"
        },
        {
            "Description": "Layer: [ground]\\nNetwork Type: [Network Traffic]\\nThreat Type: [Infiltration]\\nTimestamp: [2024-04-13 10:15:22]\\nSource IP: [**********]\\nDestination IP: [***********0]\\nProtocol: [SSH]\\nAttack Method: [Brute Force SSH]\\nDetails: Multiple failed SSH login attempts detected.",
            "Base Score": 6.2,
            "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"],
            "Threat Type": "Infiltration",
            "Advice": "Strengthen access controls and implement intrusion detection systems.",
            "Processing Time": "2024-07-31T10:33:26"
        }
    ]
    
    # 步骤1: 将Specific Advice输出转换为统一格式
    print("1. 转换Specific Advice Agent输出为统一格式...")
    unified_data = converter.convert_specific_advice_to_strategy_input(specific_advice_output)
    
    # 保存转换后的数据
    unified_file_path = "unified_workflow_outputs/unified_data_from_advice.json"
    converter.save_converted_data(unified_data, unified_file_path)
    print(f"   ✅ 统一格式数据已保存: {unified_file_path}")
    
    # 步骤2: Comprehensive Decision Agent处理统一格式数据
    print("2. Comprehensive Decision Agent处理统一格式数据...")
    result_path = agent.run(unified_file_path, "collaborative_workflow_result.json")
    print(f"   ✅ 协作流程结果已保存: {result_path}")
    
    print("\n🔄 演示场景2: 单独运行模式")
    print("-" * 50)
    
    # 检查是否存在CVSS数据文件
    cvss_file = "output_0525_finetune_metrics.json"
    if Path(cvss_file).exists():
        print("1. 转换CVSS数据为统一格式...")
        
        try:
            # 转换CVSS数据
            cvss_unified_data = converter.convert_cvss_data_to_strategy_input(cvss_file)
            
            # 保存转换后的CVSS数据
            cvss_unified_file_path = "unified_workflow_outputs/unified_data_from_cvss.json"
            converter.save_converted_data(cvss_unified_data, cvss_unified_file_path)
            print(f"   ✅ CVSS统一格式数据已保存: {cvss_unified_file_path}")
            
            # Comprehensive Decision Agent处理CVSS统一格式数据
            print("2. Comprehensive Decision Agent处理CVSS统一格式数据...")
            cvss_result_path = agent.run(cvss_unified_file_path, "standalone_cvss_result.json")
            print(f"   ✅ 单独运行结果已保存: {cvss_result_path}")
            
        except Exception as e:
            print(f"   ❌ CVSS数据处理失败: {e}")
    else:
        print(f"1. CVSS数据文件不存在: {cvss_file}")
        print("   使用策略训练数据演示单独运行...")
        
        # 使用策略训练数据的input字段演示
        strategy_file = "output_1112_strategy_train_data.json"
        if Path(strategy_file).exists():
            try:
                # 读取策略训练数据并提取input字段
                with open(strategy_file, 'r', encoding='utf-8') as f:
                    strategy_data = json.load(f)
                
                # 提取前几个示例的input字段，移除output字段
                sample_strategy_inputs = []
                for item in strategy_data[:3]:  # 只取前3个示例
                    sample_input = {
                        "instruction": item.get("instruction", ""),
                        "input": item.get("input", "")
                        # 故意不包含output字段
                    }
                    sample_strategy_inputs.append(sample_input)
                
                # 保存为统一格式
                strategy_unified_file_path = "unified_workflow_outputs/unified_data_from_strategy_input.json"
                converter.save_converted_data(sample_strategy_inputs, strategy_unified_file_path)
                print(f"   ✅ 策略输入统一格式数据已保存: {strategy_unified_file_path}")
                
                # Comprehensive Decision Agent处理策略输入数据
                print("2. Comprehensive Decision Agent处理策略输入数据...")
                strategy_result_path = agent.run(strategy_unified_file_path, "standalone_strategy_input_result.json")
                print(f"   ✅ 策略输入处理结果已保存: {strategy_result_path}")
                
            except Exception as e:
                print(f"   ❌ 策略输入数据处理失败: {e}")
        else:
            print(f"   策略训练数据文件不存在: {strategy_file}")
    
    print("\n🔄 演示场景3: 格式兼容性验证")
    print("-" * 50)
    
    # 验证不同格式的数据都能被正确处理
    test_formats = []
    
    # 检查生成的文件
    output_dir = Path("unified_workflow_outputs")
    if output_dir.exists():
        for file_path in output_dir.glob("unified_data_*.json"):
            print(f"3. 验证文件格式兼容性: {file_path.name}")
            
            try:
                # 重新加载并处理，验证格式兼容性
                test_result_path = agent.run(str(file_path), f"compatibility_test_{file_path.stem}.json")
                print(f"   ✅ 格式兼容性验证通过: {test_result_path}")
                test_formats.append(file_path.name)
                
            except Exception as e:
                print(f"   ❌ 格式兼容性验证失败: {e}")
    
    print("\n📊 统一工作流程总结")
    print("-" * 50)
    print(f"✅ 成功演示了统一数据格式的工作流程")
    print(f"✅ 协作流程和单独运行都使用相同的数据格式")
    print(f"✅ Comprehensive Decision Agent无需修改即可处理多种输入源")
    print(f"✅ 验证了 {len(test_formats)} 种数据格式的兼容性")
    
    # 显示输出文件列表
    if output_dir.exists():
        print(f"\n📁 生成的输出文件:")
        for file_path in sorted(output_dir.glob("*.json")):
            file_size = file_path.stat().st_size
            print(f"   - {file_path.name} ({file_size} bytes)")
    
    print(f"\n✅ 统一工作流程演示完成!")

def show_format_comparison():
    """展示不同格式的对比"""
    
    print("\n" + "=" * 80)
    print("数据格式对比说明")
    print("=" * 80)
    
    print("\n📋 统一后的数据流程:")
    print("1. 任何输入源 → 统一转换器 → 标准策略输入格式")
    print("2. 标准策略输入格式 → Comprehensive Decision Agent → 策略选择结果")
    
    print("\n📋 支持的输入源:")
    print("- Specific Advice Agent输出 (协作流程)")
    print("- CVSS数据文件 (单独运行)")
    print("- 策略训练数据的input字段 (单独运行)")
    
    print("\n📋 统一格式结构:")
    unified_format_example = {
        "instruction": "Please select strategies based on description...",
        "input": "IP: [***********]\\nDescription: DDoS threat identified..."
        # 注意：没有output字段，让Comprehensive Decision Agent生成
    }
    
    print(json.dumps(unified_format_example, indent=2, ensure_ascii=False))
    
    print("\n📋 优势:")
    print("✅ 统一的数据接口，无需修改Agent代码")
    print("✅ 支持协作流程和单独运行")
    print("✅ 自动格式检测和转换")
    print("✅ 保持原有功能的完整性")

def main():
    """主函数"""
    
    try:
        # 确保输出目录存在
        Path("unified_workflow_outputs").mkdir(exist_ok=True)
        
        # 运行统一工作流程演示
        demo_unified_workflow()
        
        # 显示格式对比说明
        show_format_comparison()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
