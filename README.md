# 零信任SAGIN多Agent威胁分析系统

## 📋 项目概述

本项目实现了一个基于大语言模型的多Agent协作系统，专门用于零信任空间-空中-地面综合网络(SAGIN)的威胁分析和安全决策支持。系统基于论文《Exploring LLM-Based Multi-Agent Situation Awareness for Zero-Trust Space-Air-Ground Integrated Networks》的理论框架，实现了完整的多Agent协作架构。

### 🎯 核心功能
- 智能化威胁分析和分类
- CVSS 3.1评分和安全建议
- 多Agent协作决策
- 策略选择和优先级排序
- 支持测试模式和LLM模式

## 🏗️ 系统架构

### 多Agent协作流程
```
原始威胁数据 → Summarization Agent → Prompt Agent → Specific Advice Agent → Comprehensive Decision Agent
```

### 核心组件
1. **Summarization Agent**: 威胁数据预处理和分类
2. **Prompt Agent**: 数据格式标准化转换
3. **Specific Advice Agent**: CVSS评分和安全建议
4. **Comprehensive Decision Agent**: 综合决策和策略选择

## 📁 项目结构

```
agent_simplified/
├── 📄 README.md                           # 本文档 (整合后的完整说明)
│
├── 🤖 核心Agent文件/
│   ├── Comprehensive Decision Agent.py    # 综合决策Agent
│   ├── Prompt Agent.py                    # 提示生成Agent
│   ├── Specific Advice Agent.py           # 特定建议Agent
│   └── Summarization Agent.py             # 信息摘要Agent
│
├── ⚙️ 配置和工作流/
│   ├── model_config.py                    # 统一模型配置
│   ├── data_loader.py                     # 数据加载器
│   ├── data_converter.py                  # 数据转换器
│   ├── agent_collaboration_workflow.py    # 完整协作工作流
│   ├── simple_agent_collaboration.py      # 简化协作工作流
│   ├── unified_workflow_config.py         # 统一工作流配置
│   └── demo_complete_workflow.py          # 完整Demo运行脚本
│
├── 🎯 演示脚本/
│   ├── demo_task1.py                      # Summarization Agent演示
│   ├── demo_prompt_agent.py               # Prompt Agent演示
│   ├── demo_specific_advice_agent.py      # Specific Advice Agent演示
│   ├── demo_comprehensive_decision_agent.py # Comprehensive Decision Agent演示
│   ├── demo_agent_collaboration.py        # Agent协作演示
│   ├── demo_unified_workflow.py           # 统一工作流演示
│   ├── run_collaborative_workflow.py      # 协作工作流运行脚本
│   └── quick_start.py                     # 快速启动脚本
│
├── 📊 数据文件/
│   ├── output_0515_dataset_fin.json       # 威胁数据集 (必需)
│   ├── output_0525_finetune_metrics.json  # CVSS评分数据 (必需)
│   └── output_1112_strategy_train_data.json # 策略训练数据 (必需)
│
└── 🔧 配置文件/
    ├── model_config.json                  # 模型配置
    ├── custom_model_config.json           # 自定义配置
    ├── requirements.txt                   # 依赖文件
    ├── install.sh                         # Linux/macOS安装脚本
    └── install.bat                        # Windows安装脚本
```

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10/macOS 10.15/Ubuntu 18.04+
- **Python版本**: 3.8+
- **内存**: 8GB RAM (测试模式) / 16GB RAM (LLM模式)
- **存储空间**: 5GB可用空间

### 1. 环境安装

#### 自动安装 (推荐)
```bash
# Linux/macOS
./install.sh

# Windows
install.bat
```

#### 手动安装
```bash
# 创建虚拟环境
conda create -n sagin-env python=3.8
conda activate sagin-env

# 安装基础依赖 (测试模式)
pip install numpy pandas torch transformers

# 安装完整依赖 (LLM模式)
pip install torch transformers openai sentence-transformers faiss-cpu numpy pandas

# 验证安装
python -c "import torch, transformers, numpy, pandas; print('安装成功')"
```

### 2. 数据文件准备

确保以下数据文件存在于项目根目录：
```
├── output_0515_dataset_fin.json          # 威胁数据集 (必需)
├── output_0525_finetune_metrics.json     # CVSS评分数据 (必需)
└── output_1112_strategy_train_data.json  # 策略训练数据 (必需)
```

### 3. 运行系统

#### 快速启动
```bash
# 一键启动 (推荐)
python quick_start.py

# 测试模式运行 - 无需模型文件
python demo_complete_workflow.py --test-mode

# 详细日志模式
python demo_complete_workflow.py --test-mode --verbose
```

#### LLM模式运行 (需要模型文件)
```bash
# 使用本地Llama3模型
python demo_complete_workflow.py --model-path ./models/llama3-7b

# 启用量化节省内存
python demo_complete_workflow.py --model-path ./models/llama3-7b --quantization
```

### 4. 单独Agent测试

```bash
# 威胁分类演示
python demo_task1.py

# 数据转换演示
python demo_prompt_agent.py

# CVSS评分演示
python demo_specific_advice_agent.py

# 策略决策演示
python demo_comprehensive_decision_agent.py

# Agent协作演示
python demo_agent_collaboration.py

# 统一工作流演示
python demo_unified_workflow.py
```

## 🎯 系统功能

### Agent详细说明

#### 1. Summarization Agent (摘要分析Agent)
- **功能**: 对原始威胁日志进行预处理和智能摘要
- **特性**: 支持多种数据格式(JSON/CSV/TXT)，分块处理大规模数据
- **输出**: 结构化摘要数据，包含威胁分类和风险评估

#### 2. Prompt Agent (提示生成Agent)
- **功能**: 动态创建和管理Specific Advice Agent
- **特性**: 7种威胁类型自动识别，语义聚类聚合相似威胁
- **输出**: 上下文感知的分析提示和Agent配置

#### 3. Specific Advice Agent (特定建议Agent)
- **功能**: 针对特定威胁类型进行深度分析
- **特性**: 5步链式思维推理，完整CVSS 3.1评分系统
- **输出**: CVSS评分、安全建议和权重计算

#### 4. Comprehensive Decision Agent (综合决策Agent)
- **功能**: 整合所有Agent分析结果，生成最终决策
- **特性**: 加权求和算法，多种冲突解决机制
- **输出**: 威胁级别评估、策略选择和行动计划

## 📊 输出结果

### 控制台输出示例
```
🔧 配置信息:
   模式: 测试模式
   模型: 规则引擎
   数据集: 3个文件

✅ 环境验证:
   Python依赖: 正常
   数据文件: 完整
   系统资源: 充足

📋 Agent执行结果:
   Summarization Agent: ✅ 处理128条威胁记录
   Prompt Agent: ✅ 生成7种威胁类型提示
   Specific Advice Agent: ✅ 生成CVSS评分和建议
   Comprehensive Decision Agent: ✅ 选择安全策略

📈 性能指标:
   总处理时间: 2.34秒
   威胁分类准确率: 92.5%
   CVSS评分一致性: 95.2%
   策略选择有效性: 88.7%
```

### 报告文件
系统会自动创建以下输出目录并保存详细报告：
- `demo_outputs/`: 完整工作流报告
- `unified_workflow_outputs/`: 统一工作流结果
- `workflow_outputs/`: 协作工作流结果

### 决策输出内容
- **整体安全评分**: 0-1.0分
- **威胁级别**: CRITICAL/HIGH/MEDIUM/LOW/MINIMAL
- **整合策略**: 优先级排序的行动计划
- **风险评估**: 保密性、完整性、可用性风险
- **置信度分数**: 决策的可靠性评估

## ⚙️ 配置选项

### 命令行参数

#### 模式选择
- `--test-mode`: 使用测试模式 (推荐，无需模型文件)
- `--llm-mode`: 使用LLM模式 (需要模型文件)

#### 模型配置
- `--model-path MODEL_PATH`: 模型路径 (默认: ./models/llama3-7b)
- `--model-type MODEL_TYPE`: 模型类型 (默认: llama3-7b)
- `--quantization`: 启用4-bit量化
- `--device DEVICE`: 设备选择 (auto/cpu/cuda)

#### 生成参数
- `--max-length MAX_LENGTH`: 最大生成长度 (默认: 2048)
- `--temperature TEMPERATURE`: 生成温度 (默认: 0.7)
- `--top-p TOP_P`: Top-p采样 (默认: 0.9)

#### 输出选项
- `--verbose`: 详细日志
- `--quiet`: 静默模式
- `--full-workflow`: 完整工作流

### 配置文件

#### model_config.json
```json
{
  "model_type": "llama3-7b",
  "model_path": "./models/llama3-7b",
  "max_length": 2048,
  "temperature": 0.7,
  "top_p": 0.9,
  "quantization": true,
  "device": "auto",
  "test_mode": false
}
```

#### custom_model_config.json
```json
{
  "openai_api_key": "your-api-key",
  "openai_model": "gpt-3.5-turbo",
  "use_openai": false,
  "batch_size": 4,
  "max_workers": 2
}
```

## 🔧 故障排除

### 常见问题

#### 1. 数据文件缺失
```
❌ 错误: 数据文件缺失: output_0515_dataset_fin.json
```
**解决方案**: 确保所有必需的数据文件存在于项目根目录

#### 2. Python依赖缺失
```
❌ 错误: ModuleNotFoundError: No module named 'transformers'
```
**解决方案**:
```bash
pip install transformers torch numpy pandas
```

#### 3. 内存不足 (LLM模式)
```
❌ 错误: CUDA out of memory
```
**解决方案**:
- 使用测试模式: `--test-mode`
- 启用量化: `--quantization`
- 使用CPU模式: `--device cpu`

#### 4. 模型路径不存在 (LLM模式)
```
❌ 错误: 模型路径不存在: ./models/llama3-7b
```
**解决方案**: 使用测试模式 `--test-mode` 或下载正确的模型文件

### 调试技巧

#### 启用详细日志
```bash
python demo_complete_workflow.py --test-mode --verbose
```

#### 单步调试
```bash
# 逐个测试Agent
python demo_task1.py
python demo_prompt_agent.py
python demo_specific_advice_agent.py
python demo_comprehensive_decision_agent.py
```

#### 验证数据格式
```bash
python -c "import json; print(json.load(open('output_0515_dataset_fin.json'))[:2])"
```

## 🚀 高级功能

### 统一工作流程
系统支持统一的数据转换和处理流程，可以处理多种输入格式：

```python
from unified_workflow_config import quick_process

# 快速处理任何格式的输入文件
result_path = quick_process("input_data.json", "output_result.json")
print(f"处理完成: {result_path}")
```

### 本地大模型支持
支持本地Llama3模型的完整配置和优化：

```python
from model_config import ModelConfig, set_global_config

# 配置本地大模型
model_config = ModelConfig(
    model_path="./models/llama3-7b",
    quantization=True,
    temperature=0.7,
    test_mode=False
)
set_global_config(model_config)
```

### 协作模式和单独运行
- **协作模式**: 完整的四阶段Agent协作流程
- **单独运行**: 支持单个Agent独立运行
- **混合模式**: 灵活组合不同Agent

## 📋 最佳实践

### 开发环境
1. 使用**测试模式**进行开发和调试
2. 启用**详细日志**便于问题定位
3. **单独测试Agent**验证功能正确性

### 生产环境
1. 使用**LLM模式**获得最佳分析效果
2. 配置适当的**资源限制**避免系统过载
3. 设置**监控和告警**及时发现问题

## 📞 获取帮助

### 查看帮助信息
```bash
python demo_complete_workflow.py --help
```

### 技术文档
本README.md文档已整合了所有重要信息，包括：
- 系统架构和Agent详细说明
- 本地大模型配置和使用方法
- 统一工作流程解决方案
- 完整的安装和运行指南
- 故障排除和最佳实践

### 快速验证
```bash
# 运行快速启动脚本
python quick_start.py

# 运行统一工作流演示
python demo_unified_workflow.py
```

## 🎉 开始使用

推荐从快速启动开始：

```bash
# 一键启动
python quick_start.py

# 或使用测试模式
python demo_complete_workflow.py --test-mode --verbose
```

这将运行完整的Agent协作流程，展示系统的所有功能，无需任何模型文件或复杂配置。

---

**项目版本**: v1.0.0
**最后更新**: 2025-07-31
**许可证**: MIT License
