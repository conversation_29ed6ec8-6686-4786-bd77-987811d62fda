#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
零信任SAGIN多Agent威胁分析系统 - 快速启动脚本
提供简单的交互式启动方式
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印系统横幅"""
    print("=" * 80)
    print("🛡️  零信任SAGIN多Agent威胁分析系统 - 精简版")
    print("=" * 80)
    print()

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查系统依赖...")
    
    required_packages = ['numpy', 'pandas', 'torch', 'transformers']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def check_data_files():
    """检查数据文件是否存在"""
    print("\n📊 检查数据文件...")
    
    required_files = [
        'output_0515_dataset_fin.json',
        'output_0525_finetune_metrics.json', 
        'output_1112_strategy_train_data.json'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} (缺失)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  缺少数据文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有数据文件检查通过")
    return True

def show_menu():
    """显示主菜单"""
    print("\n🎯 请选择运行模式:")
    print("1. 完整Demo运行 (测试模式) - 推荐")
    print("2. 完整Demo运行 (详细日志)")
    print("3. 单独Agent测试")
    print("4. Agent协作演示")
    print("5. 查看帮助信息")
    print("6. 退出")
    print()

def run_complete_demo(verbose=False):
    """运行完整Demo"""
    print("\n🚀 启动完整Demo运行...")
    
    cmd = ["python", "demo_complete_workflow.py", "--test-mode"]
    if verbose:
        cmd.append("--verbose")
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 运行失败: {e}")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断运行")

def run_single_agent_test():
    """运行单独Agent测试"""
    print("\n🤖 选择要测试的Agent:")
    print("1. Summarization Agent (威胁分类)")
    print("2. Prompt Agent (数据转换)")
    print("3. Specific Advice Agent (CVSS评分)")
    print("4. Comprehensive Decision Agent (策略决策)")
    print("5. 返回主菜单")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    scripts = {
        '1': 'demo_task1.py',
        '2': 'demo_prompt_agent.py', 
        '3': 'demo_specific_advice_agent.py',
        '4': 'demo_comprehensive_decision_agent.py'
    }
    
    if choice in scripts:
        print(f"\n🚀 启动 {scripts[choice]}...")
        try:
            subprocess.run(["python", scripts[choice]], check=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ 运行失败: {e}")
        except KeyboardInterrupt:
            print("\n⏹️  用户中断运行")
    elif choice == '5':
        return
    else:
        print("❌ 无效选择")

def run_collaboration_demo():
    """运行Agent协作演示"""
    print("\n🤝 启动Agent协作演示...")
    
    try:
        subprocess.run(["python", "demo_agent_collaboration.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 运行失败: {e}")
    except KeyboardInterrupt:
        print("\n⏹️  用户中断运行")

def show_help():
    """显示帮助信息"""
    print("\n📖 帮助信息:")
    print()
    print("系统功能:")
    print("- 威胁数据分析和分类")
    print("- CVSS 3.1评分计算")
    print("- 安全策略推荐")
    print("- 多Agent协作决策")
    print()
    print("运行模式:")
    print("- 测试模式: 无需模型文件，快速演示")
    print("- LLM模式: 需要本地模型，真实推理")
    print()
    print("文档参考:")
    print("- README.md: 快速开始指南")
    print("- 运行方法说明.md: 详细运行方法")
    print("- 使用说明文档.md: 完整使用说明")
    print()
    print("命令行运行:")
    print("python demo_complete_workflow.py --test-mode")
    print("python demo_complete_workflow.py --help")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_dependencies():
        print("\n❌ 环境检查失败，请先安装依赖")
        return
    
    if not check_data_files():
        print("\n❌ 数据文件检查失败，请确保数据文件存在")
        return
    
    print("\n✅ 系统环境检查通过，可以开始使用！")
    
    # 主循环
    while True:
        show_menu()
        choice = input("请选择 (1-6): ").strip()
        
        if choice == '1':
            run_complete_demo(verbose=False)
        elif choice == '2':
            run_complete_demo(verbose=True)
        elif choice == '3':
            run_single_agent_test()
        elif choice == '4':
            run_collaboration_demo()
        elif choice == '5':
            show_help()
        elif choice == '6':
            print("\n👋 感谢使用！")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户退出，感谢使用！")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        print("请查看文档或联系技术支持")
