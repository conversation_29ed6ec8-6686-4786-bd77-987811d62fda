#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
协作工作流程运行脚本 - 使用本地大模型
支持完整的四阶段协作流程：Summarization → Prompt → Specific Advice → Comprehensive Decision
"""

import os
import sys
import json
import time
import argparse
import importlib.util
from pathlib import Path
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model_config import ModelConfig, set_global_config, get_global_config
from unified_workflow_config import create_unified_workflow, UnifiedWorkflowConfig

def load_agent_module(agent_name: str, file_path: str):
    """动态加载Agent模块"""
    try:
        spec = importlib.util.spec_from_file_location(agent_name, file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module
    except Exception as e:
        print(f"❌ 加载{agent_name}失败: {e}")
        return None

def run_full_collaborative_workflow(input_data_file: str, output_dir: str = "./collaborative_outputs"):
    """运行完整的协作工作流程"""
    
    print("=" * 80)
    print("🤝 协作工作流程 - 使用本地大模型")
    print("=" * 80)
    
    # 确保输出目录存在
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    start_time = time.time()
    results = {}
    
    try:
        # 步骤1: Summarization Agent
        print("\n📊 步骤1: 运行Summarization Agent...")
        summarization_module = load_agent_module("summarization_agent", "Summarization Agent.py")
        if summarization_module is None:
            raise Exception("无法加载Summarization Agent")
        
        summarization_agent = summarization_module.SummarizationAgent()
        summary_result = summarization_agent.run(input_data_file)
        results['summarization'] = summary_result
        print(f"   ✅ 摘要完成: {summary_result}")
        
        # 步骤2: Prompt Agent
        print("\n💡 步骤2: 运行Prompt Agent...")
        prompt_module = load_agent_module("prompt_agent", "Prompt Agent.py")
        if prompt_module is None:
            raise Exception("无法加载Prompt Agent")
        
        prompt_agent = prompt_module.PromptAgent()
        prompt_result = prompt_agent.run(summary_result)
        results['prompt'] = prompt_result
        print(f"   ✅ 提示生成完成: {prompt_result}")
        
        # 步骤3: Specific Advice Agent
        print("\n🎯 步骤3: 运行Specific Advice Agent...")
        advice_module = load_agent_module("specific_advice_agent", "Specific Advice Agent.py")
        if advice_module is None:
            raise Exception("无法加载Specific Advice Agent")
        
        advice_agent = advice_module.SpecificAdviceAgent()
        advice_result = advice_agent.run(prompt_result)
        results['specific_advice'] = advice_result
        print(f"   ✅ 具体建议生成完成: {advice_result}")
        
        # 步骤4: Comprehensive Decision Agent (使用统一工作流程)
        print("\n🎯 步骤4: 运行Comprehensive Decision Agent...")
        
        # 创建统一工作流程配置
        workflow_config = UnifiedWorkflowConfig(
            unified_data_output_dir=output_dir,
            comprehensive_agent_config={
                "output_dir": output_dir,
                "max_strategies": 10,
                "enable_explanation": True,
                "decision_threshold": 0.5,
                "conflict_resolution_method": "weighted_average"
            }
        )
        
        workflow_manager = create_unified_workflow(workflow_config)
        final_result = workflow_manager.run_collaborative_workflow(advice_result)
        results['comprehensive_decision'] = final_result
        print(f"   ✅ 综合决策完成: {final_result}")
        
        # 保存完整结果摘要
        end_time = time.time()
        execution_time = end_time - start_time
        
        summary = {
            "workflow_type": "collaborative",
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat(),
            "input_file": input_data_file,
            "output_directory": output_dir,
            "model_config": {
                "model_path": get_global_config().model_path,
                "quantization": get_global_config().quantization,
                "test_mode": get_global_config().test_mode
            },
            "results": results,
            "status": "success"
        }
        
        summary_file = Path(output_dir) / "collaborative_workflow_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 协作工作流程完成!")
        print(f"   执行时间: {execution_time:.2f}秒")
        print(f"   结果摘要: {summary_file}")
        print(f"   最终决策结果: {final_result}")
        
        return final_result
        
    except Exception as e:
        print(f"\n❌ 协作工作流程失败: {e}")
        
        # 保存错误信息
        error_summary = {
            "workflow_type": "collaborative",
            "timestamp": datetime.now().isoformat(),
            "input_file": input_data_file,
            "error": str(e),
            "partial_results": results,
            "status": "failed"
        }
        
        error_file = Path(output_dir) / "collaborative_workflow_error.json"
        with open(error_file, 'w', encoding='utf-8') as f:
            json.dump(error_summary, f, ensure_ascii=False, indent=2)
        
        return None

def run_quick_collaborative_workflow(specific_advice_output: str, output_dir: str = "./collaborative_outputs"):
    """快速协作工作流程 - 从Specific Advice Agent输出开始"""
    
    print("=" * 80)
    print("🚀 快速协作工作流程 - 从Specific Advice开始")
    print("=" * 80)
    
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    start_time = time.time()
    
    try:
        # 创建统一工作流程
        workflow_config = UnifiedWorkflowConfig(
            unified_data_output_dir=output_dir,
            comprehensive_agent_config={
                "output_dir": output_dir,
                "max_strategies": 10,
                "enable_explanation": True
            }
        )
        
        workflow_manager = create_unified_workflow(workflow_config)
        
        print(f"📁 输入文件: {specific_advice_output}")
        print("🔄 开始处理...")
        
        result_path = workflow_manager.run_collaborative_workflow(specific_advice_output)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"\n✅ 快速协作流程完成!")
        print(f"   执行时间: {execution_time:.2f}秒")
        print(f"   结果文件: {result_path}")
        
        return result_path
        
    except Exception as e:
        print(f"\n❌ 快速协作流程失败: {e}")
        return None

def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(description="协作工作流程运行脚本")
    
    # 输入输出参数
    parser.add_argument("--input", "-i", required=True, help="输入数据文件路径")
    parser.add_argument("--output-dir", "-o", default="./collaborative_outputs", help="输出目录")
    
    # 工作流程模式
    parser.add_argument("--mode", choices=["full", "quick"], default="full", 
                       help="工作流程模式: full=完整四阶段, quick=从Specific Advice开始")
    
    # 模型配置
    parser.add_argument("--model-path", default="./models/llama3-7b", help="模型路径")
    parser.add_argument("--quantization", action="store_true", help="启用4-bit量化")
    parser.add_argument("--test-mode", action="store_true", help="使用测试模式")
    parser.add_argument("--device", default="auto", help="设备选择 (auto/cpu/cuda)")
    
    # 生成参数
    parser.add_argument("--temperature", type=float, default=0.7, help="生成温度")
    parser.add_argument("--max-length", type=int, default=2048, help="最大生成长度")
    
    # 其他选项
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not os.path.exists(args.input):
        print(f"❌ 输入文件不存在: {args.input}")
        return 1
    
    # 配置模型
    model_config = ModelConfig(
        model_path=args.model_path,
        quantization=args.quantization,
        test_mode=args.test_mode,
        device=args.device,
        temperature=args.temperature,
        max_length=args.max_length
    )
    
    set_global_config(model_config)
    
    # 显示配置信息
    print(f"🔧 模型配置:")
    print(f"   模型路径: {model_config.model_path}")
    print(f"   量化: {'启用' if model_config.quantization else '禁用'}")
    print(f"   测试模式: {'启用' if model_config.test_mode else '禁用'}")
    print(f"   设备: {model_config.device}")
    print(f"   温度: {model_config.temperature}")
    
    # 运行工作流程
    if args.mode == "full":
        result = run_full_collaborative_workflow(args.input, args.output_dir)
    else:  # quick
        result = run_quick_collaborative_workflow(args.input, args.output_dir)
    
    if result:
        print(f"\n🎉 工作流程成功完成!")
        return 0
    else:
        print(f"\n💥 工作流程执行失败!")
        return 1

if __name__ == "__main__":
    exit(main())
