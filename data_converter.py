#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一数据转换器 - Unified Data Converter
将不同格式的数据统一转换为Comprehensive Decision Agent可处理的标准格式
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UnifiedDataConverter:
    """统一数据转换器"""
    
    def __init__(self):
        """初始化转换器"""
        
        # 加载策略训练数据中的instruction模板
        self.strategy_instruction_template = self._load_strategy_instruction_template()
        
        # 威胁类型映射
        self.threat_type_mapping = {
            "DDoS": "ddos",
            "Infiltration": "infiltration", 
            "Brute Force": "brute_force",
            "SQL Injection": "sql_injection",
            "Eavesdropping": "eavesdropping",
            "Port Scan": "port_scan",
            "ARP Spoofing": "arp_spoofing"
        }
    
    def _load_strategy_instruction_template(self) -> str:
        """加载策略训练数据中的instruction模板"""
        try:
            strategy_file_path = "output_1112_strategy_train_data.json"
            if Path(strategy_file_path).exists():
                with open(strategy_file_path, 'r', encoding='utf-8') as f:
                    strategy_data = json.load(f)
                
                if strategy_data and len(strategy_data) > 0:
                    # 使用第一个示例的instruction作为模板
                    return strategy_data[0].get('instruction', '')
            
            logger.warning("策略训练数据文件不存在，使用默认instruction模板")
            return self._get_default_instruction_template()
            
        except Exception as e:
            logger.error(f"加载策略instruction模板失败: {e}")
            return self._get_default_instruction_template()
    
    def _get_default_instruction_template(self) -> str:
        """获取默认的instruction模板"""
        return """Please select the most appropriate set of strategies from the list below based on the description, and output them in strict list format directly(['xxx', 'xxx', 'xxxx',...]),please output only a list, not any extraneous words: [["Limit Hardware Installation", "Active Directory Configuration", "SSL/TLS Inspection", "Environment Variable Permissions", "Restrict Web-Based Content"], ["Disable or Remove Feature or Program", "Encrypt Sensitive Information", "Filter Network Traffic", "Limit Access to Resource Over Network", "Network Intrusion Prevention", "Network Segmentation", "User Training"], ["Active Directory Configuration", "User Training", "Application Developer Guidance", "Password Policies", "Filter Network Traffic"], ["Privileged Process Integrity", "Data Loss Prevention", "Privileged Account Management", "Pre-compromise", "Threat Intelligence Program", "Software Configuration", "Network Intrusion Prevention"]] ,please just output the list alone.The description is following:"""
    
    def convert_specific_advice_to_strategy_input(self, specific_advice_data: Union[str, List[Dict[str, Any]]]) -> List[Dict[str, str]]:
        """
        将Specific Advice Agent的输出转换为策略训练数据格式（不含output字段）
        
        Args:
            specific_advice_data: Specific Advice Agent的输出数据（文件路径或数据列表）
            
        Returns:
            转换后的策略输入格式数据列表
        """
        
        logger.info("开始转换Specific Advice Agent输出为策略输入格式...")
        
        # 加载数据
        if isinstance(specific_advice_data, str):
            # 如果是文件路径
            with open(specific_advice_data, 'r', encoding='utf-8') as f:
                advice_data = json.load(f)
        else:
            # 如果是数据列表
            advice_data = specific_advice_data
        
        converted_data = []
        
        for item in advice_data:
            # 提取关键信息
            description = item.get('Description', '')
            threat_type = item.get('Threat Type', '')
            base_score = item.get('Base Score', 0.0)
            advice = item.get('Advice', '')
            
            # 转换为策略输入格式
            strategy_input_item = {
                "instruction": self.strategy_instruction_template,
                "input": self._format_threat_description_for_strategy_input(
                    description, threat_type, base_score, advice
                )
                # 注意：这里故意不包含output字段，让Comprehensive Decision Agent生成
            }
            
            converted_data.append(strategy_input_item)
        
        logger.info(f"成功转换 {len(converted_data)} 条记录")
        return converted_data
    
    def _format_threat_description_for_strategy_input(self, description: str, threat_type: str, base_score: float, advice: str) -> str:
        """
        将威胁信息格式化为策略输入格式
        
        Args:
            description: 威胁描述
            threat_type: 威胁类型
            base_score: CVSS基础评分
            advice: 建议
            
        Returns:
            格式化后的输入字符串
        """
        
        # 提取IP地址信息
        ip_info = self._extract_ip_from_description(description)
        
        # 提取攻击方法
        attack_method = self._extract_attack_method_from_description(description)
        
        # 提取影响信息
        impact = self._extract_impact_from_description(description, advice)
        
        # 提取漏洞信息
        vulnerability = self._extract_vulnerability_from_description(description, advice)
        
        # 构建标准化的输入格式（模仿output_1112_strategy_train_data.json中的input格式）
        formatted_input = f"IP: [{ip_info}]\\nDescription: {threat_type} threat identified"
        
        if attack_method:
            formatted_input += f".\\nAttack Method: {attack_method}"
        
        if impact:
            formatted_input += f".\\nImpact: {impact}"
        
        if vulnerability:
            formatted_input += f".\\nVulnerability: {vulnerability}"
        
        formatted_input += f".\\nCVSS Score: {base_score}"
        
        return formatted_input
    
    def _extract_ip_from_description(self, description: str) -> str:
        """从描述中提取IP地址"""
        import re
        
        # 查找IP地址模式
        ip_patterns = [
            r'Source IP:\s*\[([^\]]+)\]',
            r'Origin IP:\s*\[([^\]]+)\]',
            r'IP:\s*\[([^\]]+)\]',
            r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
        ]
        
        for pattern in ip_patterns:
            match = re.search(pattern, description)
            if match:
                return match.group(1)
        
        return "Unknown"
    
    def _extract_attack_method_from_description(self, description: str) -> str:
        """从描述中提取攻击方法"""
        
        # 常见攻击方法关键词
        attack_methods = {
            "DDoS": "Distributed Denial of Service attack",
            "Brute Force": "Brute force authentication attack",
            "SQL Injection": "SQL injection attack",
            "Port Scan": "Network port scanning",
            "Eavesdropping": "Communication interception",
            "ARP Spoofing": "ARP cache poisoning"
        }
        
        description_lower = description.lower()
        
        for method, full_name in attack_methods.items():
            if method.lower() in description_lower:
                return full_name
        
        # 如果没有找到特定方法，尝试提取Attack Method字段
        import re
        match = re.search(r'Attack Method:\s*\[([^\]]+)\]', description)
        if match:
            return match.group(1)
        
        return "Unknown attack method"
    
    def _extract_impact_from_description(self, description: str, advice: str) -> str:
        """从描述和建议中提取影响信息"""
        
        # 常见影响关键词
        impact_keywords = {
            "denial of service": "Service availability disruption",
            "unauthorized access": "Potential unauthorized system access",
            "data breach": "Risk of sensitive data exposure",
            "confidential": "Threat of confidential information exposure",
            "integrity": "Data integrity compromise risk",
            "availability": "System availability impact"
        }
        
        combined_text = (description + " " + advice).lower()
        
        for keyword, impact_desc in impact_keywords.items():
            if keyword in combined_text:
                return impact_desc
        
        return "Potential security impact to system operations"
    
    def _extract_vulnerability_from_description(self, description: str, advice: str) -> str:
        """从描述和建议中提取漏洞信息"""
        
        # 常见漏洞关键词
        vulnerability_keywords = {
            "unencrypted": "Lack of encryption",
            "weak password": "Weak authentication mechanisms",
            "open port": "Exposed network services",
            "outdated": "Outdated software components",
            "misconfiguration": "System misconfiguration"
        }
        
        combined_text = (description + " " + advice).lower()
        
        for keyword, vuln_desc in vulnerability_keywords.items():
            if keyword in combined_text:
                return vuln_desc
        
        return "System security weakness"
    
    def convert_cvss_data_to_strategy_input(self, cvss_file_path: str = "output_0525_finetune_metrics.json") -> List[Dict[str, str]]:
        """
        将CVSS数据文件转换为策略输入格式
        
        Args:
            cvss_file_path: CVSS数据文件路径
            
        Returns:
            转换后的策略输入格式数据列表
        """
        
        logger.info(f"开始转换CVSS数据文件: {cvss_file_path}")
        
        with open(cvss_file_path, 'r', encoding='utf-8') as f:
            cvss_data = json.load(f)
        
        # 展平嵌套的CVSS数据结构
        flattened_data = self._flatten_cvss_data(cvss_data)
        
        converted_data = []
        
        for item in flattened_data:
            # 转换为Specific Advice格式，然后再转换为策略输入格式
            advice_format_item = {
                "Description": item.get("Description", ""),
                "Base Score": item.get("Base Score", 0.0),
                "Metrics": item.get("Metrics", []),
                "Threat Type": self._classify_threat_type_from_description(item.get("Description", "")),
                "Advice": self._generate_basic_advice_from_cvss(item)
            }
            
            # 转换为策略输入格式
            strategy_input_item = {
                "instruction": self.strategy_instruction_template,
                "input": self._format_threat_description_for_strategy_input(
                    advice_format_item["Description"],
                    advice_format_item["Threat Type"],
                    advice_format_item["Base Score"],
                    advice_format_item["Advice"]
                )
            }
            
            converted_data.append(strategy_input_item)
        
        logger.info(f"成功转换 {len(converted_data)} 条CVSS记录")
        return converted_data
    
    def _flatten_cvss_data(self, cvss_data: List) -> List[Dict[str, Any]]:
        """展平嵌套的CVSS数据结构"""
        
        flattened = []
        
        def flatten_recursive(data):
            if isinstance(data, list):
                for item in data:
                    flatten_recursive(item)
            elif isinstance(data, dict):
                # 如果是字典且包含CVSS字段，则添加到结果中
                if "Description" in data and "Base Score" in data:
                    flattened.append(data)
                else:
                    # 否则继续递归
                    for value in data.values():
                        flatten_recursive(value)
        
        flatten_recursive(cvss_data)
        return flattened
    
    def _classify_threat_type_from_description(self, description: str) -> str:
        """从描述中分类威胁类型"""
        
        description_lower = description.lower()
        
        # 威胁类型关键词映射
        threat_keywords = {
            "DDoS": ["ddos", "denial of service", "flood", "loit"],
            "Infiltration": ["brute force", "login", "ssh", "unauthorized"],
            "Port Scan": ["port scan", "nmap", "scanning"],
            "Eavesdropping": ["eavesdrop", "intercept", "unencrypted"],
            "ARP Spoofing": ["arp", "spoofing", "poisoning"],
            "SQL Injection": ["sql", "injection"],
            "Malware": ["malware", "virus", "trojan"]
        }
        
        for threat_type, keywords in threat_keywords.items():
            for keyword in keywords:
                if keyword in description_lower:
                    return threat_type
        
        return "Unknown"
    
    def _generate_basic_advice_from_cvss(self, cvss_item: Dict[str, Any]) -> str:
        """从CVSS数据生成基础建议"""
        
        base_score = cvss_item.get("Base Score", 0.0)
        description = cvss_item.get("Description", "")
        
        # 根据CVSS评分和描述生成基础建议
        if base_score >= 9.0:
            severity = "Critical"
            urgency = "immediate"
        elif base_score >= 7.0:
            severity = "High"
            urgency = "urgent"
        elif base_score >= 4.0:
            severity = "Medium"
            urgency = "timely"
        else:
            severity = "Low"
            urgency = "routine"
        
        threat_type = self._classify_threat_type_from_description(description)
        
        # 生成针对性建议
        advice_templates = {
            "DDoS": f"{severity} priority: Implement DDoS protection measures, rate limiting, and traffic filtering.",
            "Infiltration": f"{severity} priority: Strengthen access controls, implement multi-factor authentication, and monitor login attempts.",
            "Port Scan": f"{severity} priority: Configure firewall rules, disable unnecessary services, and implement intrusion detection.",
            "Eavesdropping": f"{severity} priority: Implement encryption for communications and monitor for unauthorized access.",
            "ARP Spoofing": f"{severity} priority: Implement ARP monitoring, static ARP entries, and network segmentation.",
            "SQL Injection": f"{severity} priority: Implement input validation, parameterized queries, and web application firewall."
        }
        
        return advice_templates.get(threat_type, f"{severity} priority: Implement appropriate security measures based on threat analysis.")
    
    def save_converted_data(self, converted_data: List[Dict[str, str]], output_path: str) -> str:
        """
        保存转换后的数据
        
        Args:
            converted_data: 转换后的数据
            output_path: 输出文件路径
            
        Returns:
            保存的文件路径
        """
        
        # 确保输出目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 添加元数据
        output_data = {
            "conversion_metadata": {
                "conversion_time": datetime.now().isoformat(),
                "converter_version": "1.0",
                "total_records": len(converted_data),
                "format": "strategy_input_without_output"
            },
            "data": converted_data
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"转换后的数据已保存到: {output_path}")
        return output_path


def main():
    """主函数 - 演示数据转换功能"""
    
    print("=" * 80)
    print("统一数据转换器演示")
    print("=" * 80)
    
    converter = UnifiedDataConverter()
    
    # 示例1: 转换Specific Advice Agent输出
    print("\n1. 转换Specific Advice Agent输出为策略输入格式...")
    
    # 模拟Specific Advice Agent输出数据
    sample_advice_data = [
        {
            "Description": "Layer: [ground]\\nNetwork Type: [Network Traffic]\\nThreat Type: [DDoS]\\nTimestamp: [2024-04-13 09:30:17]\\nSource IP: [**********]\\nDestination IP: [*************]\\nProtocol: [TCP]\\nAttack Method: [DDoS LOIT attack]\\nDetails: A DDoS LOIT attack aimed at Ubuntu16 has been identified.",
            "Base Score": 7.4,
            "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"],
            "Threat Type": "DDoS",
            "Advice": "Implement DDoS protection measures, rate limiting, and traffic filtering.",
            "Processing Time": "2024-07-31T10:33:25"
        }
    ]
    
    converted_strategy_input = converter.convert_specific_advice_to_strategy_input(sample_advice_data)
    
    print(f"✅ 成功转换 {len(converted_strategy_input)} 条记录")
    print("转换后的格式示例:")
    print(json.dumps(converted_strategy_input[0], indent=2, ensure_ascii=False))
    
    # 保存转换结果
    output_path = converter.save_converted_data(
        converted_strategy_input, 
        "converted_outputs/strategy_input_from_advice.json"
    )
    
    print(f"\n💾 转换结果已保存到: {output_path}")
    
    # 示例2: 转换CVSS数据（如果文件存在）
    cvss_file = "output_0525_finetune_metrics.json"
    if Path(cvss_file).exists():
        print(f"\n2. 转换CVSS数据文件: {cvss_file}")
        
        try:
            converted_cvss_data = converter.convert_cvss_data_to_strategy_input(cvss_file)
            print(f"✅ 成功转换 {len(converted_cvss_data)} 条CVSS记录")
            
            # 保存CVSS转换结果
            cvss_output_path = converter.save_converted_data(
                converted_cvss_data,
                "converted_outputs/strategy_input_from_cvss.json"
            )
            print(f"💾 CVSS转换结果已保存到: {cvss_output_path}")
            
        except Exception as e:
            print(f"❌ CVSS数据转换失败: {e}")
    else:
        print(f"\n2. CVSS数据文件不存在: {cvss_file}")
    
    print("\n✅ 数据转换演示完成!")


if __name__ == "__main__":
    main()
