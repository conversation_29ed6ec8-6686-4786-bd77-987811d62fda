import json
import os
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from datetime import datetime
from collections import defaultdict
import hashlib
from enum import Enum
import math
import re

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import openai
# 可选依赖，测试模式下不需要
try:
    from sentence_transformers import SentenceTransformer
    import faiss
    VECTOR_DEPS_AVAILABLE = True
except ImportError:
    VECTOR_DEPS_AVAILABLE = False
    # logger会在后面定义，这里先跳过

# 导入统一的模型配置
from model_config import ModelConfig, get_global_config, get_global_llm, LLMInterface

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ComprehensiveDecisionConfig:
    """配置类，用于管理Comprehensive Decision Agent的参数"""
    embedding_model: str = "all-MiniLM-L6-v2"
    vector_db_path: str = "./vector_db"
    max_length: int = 4096
    temperature: float = 0.7
    top_p: float = 0.9
    output_dir: str = "./comprehensive_decision_outputs"
    quantization: bool = True
    decision_threshold: float = 0.5
    conflict_resolution_method: str = "weighted_average"  # weighted_average, majority_vote, highest_confidence
    max_strategies: int = 10
    enable_explanation: bool = True

class ThreatLevel(Enum):
    """威胁级别枚举"""
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"
    MINIMAL = "MINIMAL"

class StrategyPriority(Enum):
    """策略优先级枚举"""
    IMMEDIATE = "IMMEDIATE"
    SHORT_TERM = "SHORT_TERM"
    MEDIUM_TERM = "MEDIUM_TERM"
    LONG_TERM = "LONG_TERM"

@dataclass
class IntegratedStrategy:
    """整合策略数据类"""
    strategy_id: str
    threat_categories: List[str]
    priority: StrategyPriority
    actions: List[str]
    resources_required: List[str]
    timeline: str
    effectiveness_score: float
    implementation_complexity: str
    dependencies: List[str]

@dataclass
class DecisionResult:
    """决策结果数据类"""
    decision_id: str
    overall_security_level: float
    threat_level: ThreatLevel
    integrated_strategy: IntegratedStrategy
    detailed_analysis: str
    confidence_score: float
    contributing_agents: List[str]
    risk_assessment: Dict[str, float]
    recommendations: List[str]
    action_plan: List[Dict[str, Any]]
    created_at: str

@dataclass
class ConflictResolution:
    """冲突解决数据类"""
    conflicting_strategies: List[str]
    resolution_method: str
    final_decision: str
    confidence: float
    reasoning: str

class LLMInterface:
    """LLM接口类"""
    
    def __init__(self, config: ComprehensiveDecisionConfig):
        self.config = config
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        if config.model_type in ["llama3-8b", "llama3-70b"]:
            self._initialize_llama()
        elif config.model_type in ["chatgpt-3.5", "chatgpt-4"]:
            self._initialize_chatgpt()
        else:
            raise ValueError(f"Unsupported model type: {config.model_type}")
    
    def _initialize_llama(self):
        """初始化Llama模型"""
        if self.config.quantization:
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
        else:
            quantization_config = None
        
        logger.info(f"Loading model: {self.config.model_path}")
        self.tokenizer = AutoTokenizer.from_pretrained(self.config.model_path)
        self.model = AutoModelForCausalLM.from_pretrained(
            self.config.model_path,
            quantization_config=quantization_config,
            device_map="auto",
            torch_dtype=torch.float16
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def _initialize_chatgpt(self):
        """初始化ChatGPT模型"""
        self.client = openai.OpenAI()
        self.model_name = "gpt-3.5-turbo" if self.config.model_type == "chatgpt-3.5" else "gpt-4"
    
    def generate_response(self, prompt: str) -> str:
        """生成响应"""
        if self.config.model_type in ["llama3-8b", "llama3-70b"]:
            return self._generate_llama_response(prompt)
        else:
            return self._generate_chatgpt_response(prompt)
    
    def _generate_llama_response(self, prompt: str) -> str:
        """生成Llama响应"""
        inputs = self.tokenizer(
            prompt, 
            return_tensors="pt", 
            truncation=True, 
            max_length=self.config.max_length
        ).to(self.device)
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=1024,
                temperature=self.config.temperature,
                top_p=self.config.top_p,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        response = self.tokenizer.decode(
            outputs[0][inputs['input_ids'].shape[1]:], 
            skip_special_tokens=True
        )
        return response.strip()
    
    def _generate_chatgpt_response(self, prompt: str) -> str:
        """生成ChatGPT响应"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1024,
                temperature=self.config.temperature,
                top_p=self.config.top_p
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"ChatGPT API error: {e}")
            return ""

class StrategyIntegrator:
    """策略整合器"""
    
    def __init__(self, config: ComprehensiveDecisionConfig):
        self.config = config
        # 只在非测试模式且依赖可用时初始化embedding模型
        global_config = get_global_config()
        if not global_config.test_mode and VECTOR_DEPS_AVAILABLE:
            try:
                self.embedding_model = SentenceTransformer(config.embedding_model)
            except Exception as e:
                logger.warning(f"Failed to load embedding model: {e}")
                self.embedding_model = None
        else:
            self.embedding_model = None
    
    def integrate_strategies(self, advice_results: List[Dict[str, Any]]) -> List[IntegratedStrategy]:
        """整合策略"""
        logger.info("Integrating strategies from all agents...")
        
        # 按威胁类型分组策略
        grouped_strategies = self._group_strategies_by_threat(advice_results)
        
        # 生成整合策略
        integrated_strategies = []
        
        for threat_category, strategies in grouped_strategies.items():
            # 分析策略相似性
            strategy_clusters = self._cluster_similar_strategies(strategies)
            
            # 为每个聚类生成整合策略
            for cluster in strategy_clusters:
                integrated_strategy = self._create_integrated_strategy(cluster, threat_category)
                integrated_strategies.append(integrated_strategy)
        
        # 排序策略按优先级
        integrated_strategies.sort(key=lambda x: self._get_priority_score(x.priority), reverse=True)
        
        return integrated_strategies[:self.config.max_strategies]
    
    def _group_strategies_by_threat(self, advice_results: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按威胁类型分组策略"""
        grouped = defaultdict(list)
        
        for result in advice_results:
            threat_category = result.get('threat_category', 'UNKNOWN')
            grouped[threat_category].append(result)
        
        return dict(grouped)
    
    def _cluster_similar_strategies(self, strategies: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """聚类相似策略"""
        if len(strategies) <= 1:
            return [strategies]
        
        # 检查是否为测试模式或embedding模型不可用
        if self.embedding_model is None:
            # 测试模式：基于威胁类型简单分组
            return self._simple_threat_type_clustering(strategies)
        
        # 提取策略文本
        strategy_texts = [s.get('strategy', '') for s in strategies]
        
        # 计算嵌入向量
        embeddings = self.embedding_model.encode(strategy_texts)
        
        # 使用K-means聚类
        num_clusters = min(3, len(strategies))
        
        # 简单的相似性聚类
        clusters = []
        used_indices = set()
        
        for i, embedding in enumerate(embeddings):
            if i in used_indices:
                continue
                
            cluster = [strategies[i]]
            used_indices.add(i)
            
            # 查找相似的策略
            for j, other_embedding in enumerate(embeddings):
                if j in used_indices:
                    continue
                
                # 计算余弦相似度
                similarity = np.dot(embedding, other_embedding) / (
                    np.linalg.norm(embedding) * np.linalg.norm(other_embedding)
                )
                
                if similarity > 0.7:  # 相似性阈值
                    cluster.append(strategies[j])
                    used_indices.add(j)
            
            clusters.append(cluster)
        
        return clusters
    
    def _simple_threat_type_clustering(self, strategies: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """测试模式：基于威胁类型的简单聚类"""
        clusters = {}
        
        for strategy in strategies:
            threat_category = strategy.get('threat_category', 'UNKNOWN')
            if threat_category not in clusters:
                clusters[threat_category] = []
            clusters[threat_category].append(strategy)
        
        return list(clusters.values())
    
    def _create_integrated_strategy(self, strategy_cluster: List[Dict[str, Any]], threat_category: str) -> IntegratedStrategy:
        """创建整合策略"""
        
        # 生成策略ID
        strategy_id = hashlib.md5(f"{threat_category}_{len(strategy_cluster)}".encode()).hexdigest()[:8]
        
        # 提取所有行动
        all_actions = []
        all_recommendations = []
        
        for strategy in strategy_cluster:
            # 提取推荐行动
            recommendations = strategy.get('recommendations', [])
            all_recommendations.extend(recommendations)
            
            # 从策略文本中提取行动
            strategy_text = strategy.get('strategy', '')
            actions = self._extract_actions_from_text(strategy_text)
            all_actions.extend(actions)
        
        # 去重并排序
        unique_actions = list(set(all_actions + all_recommendations))
        
        # 计算平均效果评分
        effectiveness_scores = [s.get('security_score', 0.0) for s in strategy_cluster]
        avg_effectiveness = sum(effectiveness_scores) / len(effectiveness_scores) if effectiveness_scores else 0.0
        
        # 确定优先级
        priority = self._determine_priority(strategy_cluster)
        
        # 生成资源需求
        resources = self._identify_required_resources(unique_actions)
        
        # 生成时间线
        timeline = self._generate_timeline(priority, len(unique_actions))
        
        # 评估实施复杂度
        complexity = self._assess_implementation_complexity(unique_actions)
        
        # 识别依赖关系
        dependencies = self._identify_dependencies(unique_actions)
        
        return IntegratedStrategy(
            strategy_id=strategy_id,
            threat_categories=[threat_category],
            priority=priority,
            actions=unique_actions[:8],  # 限制行动数量
            resources_required=resources,
            timeline=timeline,
            effectiveness_score=avg_effectiveness,
            implementation_complexity=complexity,
            dependencies=dependencies
        )
    
    def _extract_actions_from_text(self, text: str) -> List[str]:
        """从文本中提取行动"""
        actions = []
        
        # 寻找行动关键词
        action_keywords = [
            'implement', 'deploy', 'configure', 'monitor', 'update', 'install',
            'establish', 'create', 'develop', 'enhance', 'strengthen', 'improve'
        ]
        
        sentences = text.split('.')
        for sentence in sentences:
            sentence = sentence.strip()
            if any(keyword in sentence.lower() for keyword in action_keywords):
                if len(sentence) > 20 and len(sentence) < 200:  # 合理长度
                    actions.append(sentence)
        
        return actions[:5]  # 限制每个策略的行动数量
    
    def _determine_priority(self, strategy_cluster: List[Dict[str, Any]]) -> StrategyPriority:
        """确定策略优先级"""
        
        # 计算平均安全评分
        scores = [s.get('security_score', 0.0) for s in strategy_cluster]
        avg_score = sum(scores) / len(scores) if scores else 0.0
        
        # 检查威胁严重性
        high_severity_count = 0
        for strategy in strategy_cluster:
            cvss_metrics = strategy.get('cvss_metrics', {})
            if isinstance(cvss_metrics, dict):
                base_score = cvss_metrics.get('base_score', 0.0)
                if base_score > 7.0:
                    high_severity_count += 1
        
        # 确定优先级
        if avg_score > 0.8 or high_severity_count > 0:
            return StrategyPriority.IMMEDIATE
        elif avg_score > 0.6:
            return StrategyPriority.SHORT_TERM
        elif avg_score > 0.4:
            return StrategyPriority.MEDIUM_TERM
        else:
            return StrategyPriority.LONG_TERM
    
    def _identify_required_resources(self, actions: List[str]) -> List[str]:
        """识别所需资源"""
        resources = set()
        
        # 资源关键词映射
        resource_keywords = {
            'hardware': ['device', 'equipment', 'server', 'infrastructure'],
            'software': ['software', 'application', 'tool', 'system'],
            'personnel': ['staff', 'expert', 'team', 'specialist'],
            'network': ['network', 'bandwidth', 'connection', 'communication'],
            'security': ['encryption', 'certificate', 'firewall', 'security']
        }
        
        for action in actions:
            action_lower = action.lower()
            for resource_type, keywords in resource_keywords.items():
                if any(keyword in action_lower for keyword in keywords):
                    resources.add(resource_type)
        
        return list(resources)
    
    def _generate_timeline(self, priority: StrategyPriority, action_count: int) -> str:
        """生成时间线"""
        base_times = {
            StrategyPriority.IMMEDIATE: "0-24 hours",
            StrategyPriority.SHORT_TERM: "1-7 days",
            StrategyPriority.MEDIUM_TERM: "1-4 weeks",
            StrategyPriority.LONG_TERM: "1-3 months"
        }
        
        base_time = base_times.get(priority, "1-4 weeks")
        
        # 根据行动数量调整时间
        if action_count > 5:
            complexity_factor = " (extended due to complexity)"
            return base_time + complexity_factor
        
        return base_time
    
    def _assess_implementation_complexity(self, actions: List[str]) -> str:
        """评估实施复杂度"""
        complexity_keywords = {
            'high': ['integrate', 'develop', 'custom', 'complex', 'advanced'],
            'medium': ['configure', 'setup', 'install', 'implement'],
            'low': ['update', 'patch', 'restart', 'enable']
        }
        
        complexity_scores = {'high': 0, 'medium': 0, 'low': 0}
        
        for action in actions:
            action_lower = action.lower()
            for complexity, keywords in complexity_keywords.items():
                if any(keyword in action_lower for keyword in keywords):
                    complexity_scores[complexity] += 1
        
        # 确定总体复杂度
        if complexity_scores['high'] > len(actions) * 0.3:
            return "HIGH"
        elif complexity_scores['medium'] > len(actions) * 0.5:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _identify_dependencies(self, actions: List[str]) -> List[str]:
        """识别依赖关系"""
        dependencies = []
        
        # 常见依赖关系
        dependency_patterns = {
            'Network Infrastructure': ['network', 'connectivity', 'bandwidth'],
            'Security Policies': ['policy', 'procedure', 'governance'],
            'Training': ['training', 'education', 'awareness'],
            'Compliance': ['compliance', 'audit', 'regulation']
        }
        
        for action in actions:
            action_lower = action.lower()
            for dep_type, keywords in dependency_patterns.items():
                if any(keyword in action_lower for keyword in keywords):
                    if dep_type not in dependencies:
                        dependencies.append(dep_type)
        
        return dependencies
    
    def _get_priority_score(self, priority: StrategyPriority) -> int:
        """获取优先级分数"""
        priority_scores = {
            StrategyPriority.IMMEDIATE: 4,
            StrategyPriority.SHORT_TERM: 3,
            StrategyPriority.MEDIUM_TERM: 2,
            StrategyPriority.LONG_TERM: 1
        }
        return priority_scores.get(priority, 0)

class ConflictResolver:
    """冲突解决器"""
    
    def __init__(self, config: ComprehensiveDecisionConfig):
        self.config = config
    
    def resolve_conflicts(self, advice_results: List[Dict[str, Any]]) -> List[ConflictResolution]:
        """解决策略冲突"""
        logger.info("Resolving strategy conflicts...")
        
        conflicts = []
        
        # 检测冲突
        conflict_groups = self._detect_conflicts(advice_results)
        
        # 解决每个冲突
        for group in conflict_groups:
            resolution = self._resolve_conflict_group(group)
            conflicts.append(resolution)
        
        return conflicts
    
    def _detect_conflicts(self, advice_results: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """检测冲突"""
        conflicts = []
        
        # 简单的冲突检测：相同威胁类型但策略差异很大
        threat_groups = defaultdict(list)
        
        for result in advice_results:
            threat_category = result.get('threat_category', 'UNKNOWN')
            threat_groups[threat_category].append(result)
        
        # 检查每个威胁类型组内的冲突
        for threat_category, group in threat_groups.items():
            if len(group) > 1:
                # 检查评分差异
                scores = [r.get('security_score', 0.0) for r in group]
                if max(scores) - min(scores) > 0.4:  # 评分差异阈值
                    conflicts.append(group)
        
        return conflicts
    
    def _resolve_conflict_group(self, conflict_group: List[Dict[str, Any]]) -> ConflictResolution:
        """解决冲突组"""
        
        if self.config.conflict_resolution_method == "weighted_average":
            return self._resolve_by_weighted_average(conflict_group)
        elif self.config.conflict_resolution_method == "majority_vote":
            return self._resolve_by_majority_vote(conflict_group)
        elif self.config.conflict_resolution_method == "highest_confidence":
            return self._resolve_by_highest_confidence(conflict_group)
        else:
            return self._resolve_by_weighted_average(conflict_group)
    
    def _resolve_by_weighted_average(self, conflict_group: List[Dict[str, Any]]) -> ConflictResolution:
        """通过加权平均解决冲突"""
        
        # 计算加权平均评分
        total_weighted_score = 0.0
        total_weight = 0.0
        
        for result in conflict_group:
            weight = result.get('weight', 1.0)
            score = result.get('security_score', 0.0)
            total_weighted_score += weight * score
            total_weight += weight
        
        final_score = total_weighted_score / total_weight if total_weight > 0 else 0.0
        
        # 选择最接近加权平均的策略
        best_strategy = min(conflict_group, key=lambda x: abs(x.get('security_score', 0.0) - final_score))
        
        return ConflictResolution(
            conflicting_strategies=[r.get('agent_id', 'unknown') for r in conflict_group],
            resolution_method="weighted_average",
            final_decision=best_strategy.get('strategy', ''),
            confidence=0.8,
            reasoning=f"Selected strategy closest to weighted average score: {final_score:.2f}"
        )
    
    def _resolve_by_majority_vote(self, conflict_group: List[Dict[str, Any]]) -> ConflictResolution:
        """通过多数投票解决冲突"""
        
        # 按威胁级别分组
        threat_levels = defaultdict(list)
        for result in conflict_group:
            score = result.get('security_score', 0.0)
            if score > 0.7:
                level = "HIGH"
            elif score > 0.4:
                level = "MEDIUM"
            else:
                level = "LOW"
            threat_levels[level].append(result)
        
        # 选择多数派
        majority_level = max(threat_levels.keys(), key=lambda x: len(threat_levels[x]))
        majority_group = threat_levels[majority_level]
        
        # 从多数派中选择最高评分的策略
        best_strategy = max(majority_group, key=lambda x: x.get('security_score', 0.0))
        
        return ConflictResolution(
            conflicting_strategies=[r.get('agent_id', 'unknown') for r in conflict_group],
            resolution_method="majority_vote",
            final_decision=best_strategy.get('strategy', ''),
            confidence=len(majority_group) / len(conflict_group),
            reasoning=f"Majority vote for {majority_level} threat level with {len(majority_group)} votes"
        )
    
    def _resolve_by_highest_confidence(self, conflict_group: List[Dict[str, Any]]) -> ConflictResolution:
        """通过最高置信度解决冲突"""
        
        # 选择置信度最高的策略
        best_strategy = max(conflict_group, key=lambda x: x.get('confidence', 0.0))
        
        return ConflictResolution(
            conflicting_strategies=[r.get('agent_id', 'unknown') for r in conflict_group],
            resolution_method="highest_confidence",
            final_decision=best_strategy.get('strategy', ''),
            confidence=best_strategy.get('confidence', 0.0),
            reasoning=f"Selected strategy with highest confidence: {best_strategy.get('confidence', 0.0):.2f}"
        )

class ComprehensiveDecisionAgent:
    """综合决策代理类"""
    
    def __init__(self, config: ComprehensiveDecisionConfig):
        self.config = config
        # 使用统一的LLM配置
        self.llm = get_global_llm()
        self.strategy_integrator = StrategyIntegrator(config)
        self.conflict_resolver = ConflictResolver(config)
        
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 威胁级别映射
        self.threat_level_mapping = {
            (0.8, 1.0): ThreatLevel.CRITICAL,
            (0.6, 0.8): ThreatLevel.HIGH,
            (0.4, 0.6): ThreatLevel.MEDIUM,
            (0.2, 0.4): ThreatLevel.LOW,
            (0.0, 0.2): ThreatLevel.MINIMAL
        }
        
        # 加载策略训练数据
        self.strategy_data = None
        self._load_strategy_training_data()
    
    def _load_strategy_training_data(self):
        """加载策略训练数据"""
        try:
            strategy_file_path = "output_1112_strategy_train_data.json"
            logger.info(f"Loading strategy training data from: {strategy_file_path}")
            
            with open(strategy_file_path, 'r', encoding='utf-8') as f:
                self.strategy_data = json.load(f)
            
            logger.info(f"Loaded {len(self.strategy_data)} strategy training examples")
        except FileNotFoundError:
            logger.warning("Strategy training data file not found, using default strategies")
            self.strategy_data = []
        except Exception as e:
            logger.error(f"Error loading strategy training data: {e}")
            self.strategy_data = []
    
    def process_threat_analysis_result(self, threat_analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """处理来自Specific Advice Agent的威胁分析结果并选择策略
        
        Args:
            threat_analysis_result: 来自Specific Advice Agent的威胁分析结果
            
        Returns:
            包含策略选择列表的结果字典
        """
        logger.info("Processing threat analysis result for strategy selection...")
        
        # 提取威胁信息
        description = threat_analysis_result.get('Description', '')
        threat_type = threat_analysis_result.get('Threat Type', '')
        base_score = threat_analysis_result.get('Base Score', 0.0)
        metrics = threat_analysis_result.get('Metrics', [])
        advice = threat_analysis_result.get('Advice', '')
        
        # 直接使用原始Description作为匹配输入（与训练数据格式一致）
        input_for_matching = description
        
        # 从策略训练数据中选择合适的策略
        selected_strategies, matching_confidence = self._select_strategies_from_training_data(input_for_matching, threat_type, base_score)
        
        # 计算策略选择置信度
        confidence = self._calculate_strategy_confidence(selected_strategies, threat_type, base_score, matching_confidence)
        
        # 确定威胁优先级
        priority = self._determine_threat_priority(base_score)
        
        # 生成策略选择结果
        result = {
            'input_threat_analysis': threat_analysis_result,
            'matching_input': input_for_matching,
            'selected_strategies': selected_strategies,
            'strategy_selection_confidence': confidence,
            'matching_confidence': matching_confidence,
            'threat_priority': priority,
            'strategy_count': len(selected_strategies),
            'processing_time': datetime.now().isoformat()
        }
        
        # 保存结果
        self._save_strategy_selection_result(result)
        
        logger.info(f"Strategy selection completed. Selected {len(selected_strategies)} strategies with confidence {confidence:.2f}")
        
        return result
    
    def _build_threat_description_for_matching(self, description: str, threat_type: str, base_score: float, metrics: List[str]) -> str:
        """构建用于策略匹配的威胁描述"""
        
        # 从原始描述中提取关键信息
        key_info = []
        
        # 添加威胁类型
        if threat_type:
            key_info.append(f"Threat Type: {threat_type}")
        
        # 添加CVSS评分信息
        if base_score > 0:
            key_info.append(f"CVSS Base Score: {base_score}")
        
        # 从描述中提取关键词
        description_lower = description.lower()
        
        # 提取IP地址
        import re
        ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
        ips = re.findall(ip_pattern, description)
        if ips:
            key_info.append(f"IP: {ips[0]}")
        
        # 提取攻击方法
        attack_keywords = ['ddos', 'sql injection', 'brute force', 'infiltration', 'eavesdropping', 'disruption']
        for keyword in attack_keywords:
            if keyword in description_lower:
                key_info.append(f"Attack Method: {keyword}")
                break
        
        # 提取影响类型
        if 'confidential' in description_lower or 'privacy' in description_lower:
            key_info.append("Impact: Confidentiality threat")
        if 'integrity' in description_lower or 'data manipulation' in description_lower:
            key_info.append("Impact: Integrity threat")
        if 'availability' in description_lower or 'denial of service' in description_lower:
            key_info.append("Impact: Availability threat")
        
        # 提取漏洞信息
        if 'unencrypted' in description_lower or 'no encryption' in description_lower:
            key_info.append("Vulnerability: Lack of encryption")
        if 'monitoring' in description_lower:
            key_info.append("Vulnerability: Insufficient monitoring")
        
        return "\\n".join(key_info)
    
    def _select_strategies_from_training_data(self, input_description: str, threat_type: str, base_score: float) -> Tuple[List[str], float]:
        """从策略训练数据中选择合适的策略
        
        Args:
            input_description: 威胁描述（对应训练数据中的input字段）
            threat_type: 威胁类型
            base_score: CVSS基础评分
            
        Returns:
            Tuple[策略列表, 匹配置信度]
        """
        
        # 检查是否为测试模式
        global_config = get_global_config()
        if global_config.test_mode:
            return self._test_mode_strategy_selection(input_description, threat_type, base_score)
        
        if not self.strategy_data:
            return self._get_default_strategies(threat_type, base_score), 0.0
        
        # 寻找最匹配的训练数据示例
        best_match = None
        best_similarity = 0.0
        
        for strategy_example in self.strategy_data:
            # 使用训练数据中的input字段进行匹配
            training_input = strategy_example.get('input', '')
            similarity = self._calculate_text_similarity(input_description, training_input)
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = strategy_example
        
        # 如果找到匹配的训练数据，使用LLM + instruction + input进行策略选择
        if best_match and best_similarity > 0.1:
            logger.info(f"Found matching example with similarity: {best_similarity:.2f}, using LLM for strategy selection")
            strategies = self._use_llm_for_strategy_selection(best_match, input_description)
            if strategies:
                # 置信度基于相似度
                confidence = min(0.9, 0.5 + best_similarity * 0.4)
                return strategies, confidence
        
        # 如果没有找到匹配的策略，使用基于威胁类型的默认策略
        logger.info("No matching strategy found, using default strategies")
        return self._get_default_strategies(threat_type, base_score), 0.0
    
    def _test_mode_strategy_selection(self, input_description: str, threat_type: str, base_score: float) -> Tuple[List[str], float]:
        """测试模式：基于威胁类型匹配策略"""
        
        description_lower = input_description.lower()
        threat_type_lower = threat_type.lower()
        
        # 基于威胁类型和描述的详细策略映射
        strategy_mappings = {
            "ddos": {
                "strategies": [
                    "Deploy DDoS protection service",
                    "Implement traffic rate limiting",
                    "Configure network load balancing", 
                    "Establish incident response procedures",
                    "Monitor network traffic patterns",
                    "Implement IP blacklisting",
                    "Deploy content delivery network (CDN)"
                ],
                "confidence": 0.90
            },
            "malware": {
                "strategies": [
                    "Deploy endpoint detection and response (EDR)",
                    "Implement network segmentation",
                    "Update antivirus signatures",
                    "Conduct security awareness training",
                    "Establish malware analysis capabilities",
                    "Implement application whitelisting",
                    "Deploy email security gateway"
                ],
                "confidence": 0.88
            },
            "sql injection": {
                "strategies": [
                    "Implement web application firewall (WAF)",
                    "Deploy database activity monitoring",
                    "Conduct code security review",
                    "Implement input validation",
                    "Establish secure coding practices",
                    "Deploy runtime application self-protection (RASP)",
                    "Implement parameterized queries"
                ],
                "confidence": 0.92
            },
            "brute force": {
                "strategies": [
                    "Implement account lockout policies",
                    "Deploy multi-factor authentication (MFA)",
                    "Establish strong password requirements",
                    "Implement rate limiting for login attempts",
                    "Deploy privileged access management (PAM)",
                    "Monitor authentication logs",
                    "Implement CAPTCHA for repeated failures"
                ],
                "confidence": 0.85
            },
            "phishing": {
                "strategies": [
                    "Conduct security awareness training",
                    "Deploy email security gateway",
                    "Implement email authentication (SPF, DKIM, DMARC)",
                    "Establish incident response procedures",
                    "Deploy endpoint protection",
                    "Implement URL filtering",
                    "Conduct phishing simulation exercises"
                ],
                "confidence": 0.87
            },
            "infiltration": {
                "strategies": [
                    "Implement network segmentation",
                    "Deploy intrusion detection system (IDS)",
                    "Establish privileged account management",
                    "Implement zero-trust architecture",
                    "Deploy security information and event management (SIEM)",
                    "Conduct regular security assessments",
                    "Implement access control hardening"
                ],
                "confidence": 0.89
            },
            "spoofing": {
                "strategies": [
                    "Implement network access control (NAC)",
                    "Deploy ARP spoofing protection",
                    "Establish DNS security measures",
                    "Implement network monitoring",
                    "Deploy intrusion prevention system (IPS)",
                    "Establish secure network protocols",
                    "Implement certificate pinning"
                ],
                "confidence": 0.84
            },
            "eavesdropping": {
                "strategies": [
                    "Implement end-to-end encryption",
                    "Deploy network segmentation",
                    "Establish secure communication protocols",
                    "Implement VPN for remote access",
                    "Deploy wireless security measures",
                    "Establish data loss prevention (DLP)",
                    "Implement network access control"
                ],
                "confidence": 0.86
            }
        }
        
        # 尝试匹配威胁类型
        for threat_key, mapping in strategy_mappings.items():
            if threat_key in threat_type_lower or threat_key in description_lower:
                return mapping["strategies"], mapping["confidence"]
        
        # 基于描述中的关键词进行更细致的匹配
        if any(word in description_lower for word in ["ddos", "distributed denial", "flood", "overwhelm"]):
            return strategy_mappings["ddos"]["strategies"], strategy_mappings["ddos"]["confidence"]
        elif any(word in description_lower for word in ["malware", "virus", "trojan", "infection"]):
            return strategy_mappings["malware"]["strategies"], strategy_mappings["malware"]["confidence"]
        elif any(word in description_lower for word in ["sql", "injection", "database"]):
            return strategy_mappings["sql injection"]["strategies"], strategy_mappings["sql injection"]["confidence"]
        elif any(word in description_lower for word in ["brute", "password", "credential"]):
            return strategy_mappings["brute force"]["strategies"], strategy_mappings["brute force"]["confidence"]
        elif any(word in description_lower for word in ["phishing", "social", "email"]):
            return strategy_mappings["phishing"]["strategies"], strategy_mappings["phishing"]["confidence"]
        elif any(word in description_lower for word in ["infiltration", "penetration", "unauthorized"]):
            return strategy_mappings["infiltration"]["strategies"], strategy_mappings["infiltration"]["confidence"]
        elif any(word in description_lower for word in ["spoofing", "arp", "dns", "fake"]):
            return strategy_mappings["spoofing"]["strategies"], strategy_mappings["spoofing"]["confidence"]
        elif any(word in description_lower for word in ["eavesdrop", "intercept", "sniff"]):
            return strategy_mappings["eavesdropping"]["strategies"], strategy_mappings["eavesdropping"]["confidence"]
        else:
            # 默认通用策略，基于CVSS评分
            if base_score >= 7.0:  # 高风险
                return [
                    "Implement comprehensive security monitoring",
                    "Deploy multi-layered defense strategy",
                    "Establish incident response procedures",
                    "Conduct immediate security assessment",
                    "Implement network segmentation",
                    "Deploy threat intelligence integration",
                    "Establish security awareness training"
                ], 0.70
            elif base_score >= 4.0:  # 中等风险
                return [
                    "Enhance security monitoring",
                    "Implement access controls",
                    "Conduct security assessment",
                    "Update security policies",
                    "Establish incident response plan"
                ], 0.65
            else:  # 低风险
                return [
                    "Monitor security events",
                    "Review security policies",
                    "Conduct routine security checks",
                    "Maintain security awareness"
                ], 0.60
    
    def _use_llm_for_strategy_selection(self, matched_example: Dict[str, Any], input_description: str) -> List[str]:
        """使用大模型进行策略选择
        
        Args:
            matched_example: 匹配的训练数据示例
            input_description: 威胁描述
            
        Returns:
            策略列表
        """
        
        try:
            # 构建LLM提示，使用训练数据的instruction和input格式
            instruction = matched_example.get('instruction', '')
            
            # 构建完整的提示
            prompt = f"{instruction}\n\n{input_description}"
            
            logger.info("Using LLM for strategy selection...")
            
            # 使用LLM生成策略选择
            response = self.llm.generate_response(prompt)
            
            # 解析LLM响应，提取策略列表
            strategies = self._parse_llm_strategy_response(response)
            
            if strategies:
                logger.info(f"LLM selected {len(strategies)} strategies")
                return strategies
            else:
                logger.warning("LLM failed to generate valid strategies")
                return []
                
        except Exception as e:
            logger.error(f"Error using LLM for strategy selection: {e}")
            return []
    
    def _parse_llm_strategy_response(self, response: str) -> List[str]:
        """解析LLM的策略选择响应
        
        Args:
            response: LLM的响应文本
            
        Returns:
            策略列表
        """
        
        try:
            # 尝试直接解析为JSON列表
            import ast
            
            # 查找列表格式的响应
            response = response.strip()
            
            # 如果响应包含方括号，尝试提取列表
            if '[' in response and ']' in response:
                start = response.find('[')
                end = response.rfind(']') + 1
                list_str = response[start:end]
                
                # 尝试使用ast.literal_eval安全解析
                try:
                    strategies = ast.literal_eval(list_str)
                    if isinstance(strategies, list) and all(isinstance(s, str) for s in strategies):
                        return strategies
                except:
                    pass
                
                # 尝试使用json.loads解析
                try:
                    import json
                    strategies = json.loads(list_str)
                    if isinstance(strategies, list) and all(isinstance(s, str) for s in strategies):
                        return strategies
                except:
                    pass
            
            # 如果直接解析失败，尝试从文本中提取策略名称
            strategies = []
            lines = response.split('\n')
            
            # 预定义的策略名称列表（从训练数据中提取）
            known_strategies = {
                "Limit Hardware Installation", "Active Directory Configuration", 
                "SSL/TLS Inspection", "Environment Variable Permissions", 
                "Restrict Web-Based Content", "Disable or Remove Feature or Program",
                "Encrypt Sensitive Information", "Filter Network Traffic",
                "Limit Access to Resource Over Network", "Network Intrusion Prevention",
                "Network Segmentation", "User Training", "Application Developer Guidance",
                "Password Policies", "Privileged Process Integrity", "Data Loss Prevention",
                "Privileged Account Management", "Pre-compromise", "Threat Intelligence Program",
                "Software Configuration"
            }
            
            for line in lines:
                line = line.strip()
                for strategy in known_strategies:
                    if strategy in line:
                        if strategy not in strategies:
                            strategies.append(strategy)
            
            return strategies[:10]  # 限制策略数量
            
        except Exception as e:
            logger.error(f"Error parsing LLM strategy response: {e}")
            return []
            # 构建prompt，使用训练数据的instruction和新的input
            instruction = matched_example.get('instruction', '')
            
            # 构建完整的prompt
            full_prompt = f"{instruction}\n\n{input_description}"
            
            logger.info("Using LLM for strategy selection...")
            
            # 使用LLM生成策略选择
            response = self.llm.generate_response(full_prompt)
            
            # 解析LLM响应，提取策略列表
            strategies = self._parse_llm_strategy_response(response)
            
            if strategies:
                logger.info(f"LLM selected {len(strategies)} strategies")
                return strategies
            else:
                logger.warning("Failed to parse LLM response, falling back to training data output")
                return matched_example.get('output', [])
                
        except Exception as e:
            logger.error(f"Error using LLM for strategy selection: {e}")
            # 回退到训练数据的输出
            return matched_example.get('output', [])
    
    def _parse_llm_strategy_response(self, response: str) -> List[str]:
        """解析LLM的策略选择响应
        
        Args:
            response: LLM的响应文本
            
        Returns:
            策略列表
        """
        
        try:
            import ast
            
            # 尝试直接解析为Python列表
            if response.strip().startswith('[') and response.strip().endswith(']'):
                strategies = ast.literal_eval(response.strip())
                if isinstance(strategies, list):
                    return [str(s) for s in strategies]
            
            # 尝试从响应中提取列表格式
            import re
            
            # 查找方括号内的内容
            list_pattern = r'\[(.*?)\]'
            matches = re.findall(list_pattern, response, re.DOTALL)
            
            if matches:
                # 取最长的匹配（可能是最完整的列表）
                longest_match = max(matches, key=len)
                
                # 分割并清理策略名称
                strategies = []
                for item in longest_match.split(','):
                    item = item.strip().strip('"\'')
                    if item and len(item) > 3:  # 过滤掉太短的项目
                        strategies.append(item)
                
                return strategies
            
            # 如果无法解析，返回空列表
            logger.warning(f"Could not parse LLM response: {response[:100]}...")
            return []
            
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return []
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        
        # 简单的关键词匹配相似度计算
        text1_lower = text1.lower()
        text2_lower = text2.lower()
        
        # 提取关键词
        keywords1 = set(re.findall(r'\b\w+\b', text1_lower))
        keywords2 = set(re.findall(r'\b\w+\b', text2_lower))
        
        # 计算Jaccard相似度
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    def _get_default_strategies(self, threat_type: str, base_score: float) -> List[str]:
        """获取基于威胁类型和评分的默认策略"""
        
        # 基于威胁类型的策略映射
        threat_strategy_mapping = {
            'ddos': [
                "Network Intrusion Prevention",
                "Filter Network Traffic", 
                "Limit Access to Resource Over Network",
                "Network Segmentation",
                "User Training"
            ],
            'sql injection': [
                "Application Developer Guidance",
                "Filter Network Traffic",
                "Network Intrusion Prevention",
                "User Training",
                "Software Configuration"
            ],
            'infiltration': [
                "Privileged Account Management",
                "Active Directory Configuration",
                "Network Intrusion Prevention",
                "User Training",
                "Password Policies"
            ],
            'eavesdropping': [
                "Encrypt Sensitive Information",
                "Network Segmentation",
                "SSL/TLS Inspection",
                "Network Intrusion Prevention",
                "User Training"
            ],
            'disruption': [
                "Network Intrusion Prevention",
                "Filter Network Traffic",
                "Network Segmentation",
                "Threat Intelligence Program",
                "User Training"
            ]
        }
        
        # 根据威胁类型选择策略
        threat_type_lower = threat_type.lower()
        for key, strategies in threat_strategy_mapping.items():
            if key in threat_type_lower:
                return strategies
        
        # 如果没有匹配的威胁类型，根据CVSS评分选择通用策略
        if base_score >= 7.0:  # 高风险
            return [
                "Network Intrusion Prevention",
                "Privileged Account Management", 
                "Encrypt Sensitive Information",
                "Network Segmentation",
                "Threat Intelligence Program",
                "User Training"
            ]
        elif base_score >= 4.0:  # 中等风险
            return [
                "Network Intrusion Prevention",
                "Filter Network Traffic",
                "User Training",
                "Network Segmentation"
            ]
        else:  # 低风险
            return [
                "User Training",
                "Network Intrusion Prevention",
                "Filter Network Traffic"
            ]
    
    def _calculate_strategy_confidence(self, selected_strategies: List[str], threat_type: str, base_score: float, matching_confidence: float) -> float:
        """计算策略选择的置信度"""
        
        # 基础置信度从匹配置信度开始
        base_confidence = matching_confidence * 0.6  # 匹配置信度占60%权重
        
        # 如果有选择的策略，增加置信度
        if selected_strategies:
            base_confidence += 0.2
        
        # 根据威胁类型匹配度调整置信度
        if threat_type and threat_type.lower() in ['ddos', 'sql injection', 'infiltration', 'eavesdropping', 'disruption']:
            base_confidence += 0.1
        
        # 根据CVSS评分调整置信度
        if base_score > 0:
            if base_score >= 7.0:
                base_confidence += 0.1  # 高风险威胁，策略选择更明确
            elif base_score >= 4.0:
                base_confidence += 0.05  # 中等风险威胁
        
        # 根据策略数量调整置信度
        strategy_count = len(selected_strategies)
        if 3 <= strategy_count <= 7:  # 合理的策略数量
            base_confidence += 0.05
        elif strategy_count > 7:  # 策略过多可能降低置信度
            base_confidence -= 0.05
        
        # 确保置信度在合理范围内
        return min(max(base_confidence, 0.0), 1.0)
    
    def _determine_threat_priority(self, base_score: float) -> str:
        """根据CVSS评分确定威胁优先级"""
        
        if base_score >= 9.0:
            return "CRITICAL"
        elif base_score >= 7.0:
            return "HIGH"
        elif base_score >= 4.0:
            return "MEDIUM"
        elif base_score >= 0.1:
            return "LOW"
        else:
            return "MINIMAL"
    
    def _save_strategy_selection_result(self, result: Dict[str, Any]):
        """保存策略选择结果到文件"""
        
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            threat_type = result.get('input_threat_analysis', {}).get('Threat Type', 'unknown')
            filename = f"strategy_selection_{threat_type.lower().replace(' ', '_')}_{timestamp}.json"
            
            # 保存到输出目录
            output_path = self.output_dir / filename
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Strategy selection result saved to: {output_path}")
            
        except Exception as e:
            logger.error(f"Failed to save strategy selection result: {e}")
    

    
    def _determine_threat_priority(self, base_score: float) -> str:
        """根据CVSS评分确定威胁优先级"""
        
        if base_score >= 9.0:
            return "CRITICAL"
        elif base_score >= 7.0:
            return "HIGH"
        elif base_score >= 4.0:
            return "MEDIUM"
        elif base_score >= 0.1:
            return "LOW"
        else:
            return "MINIMAL"
    
    def _save_strategy_selection_result(self, result: Dict[str, Any]):
        """保存策略选择结果"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"strategy_selection_result_{timestamp}.json"
        filepath = self.output_dir / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Strategy selection result saved to: {filepath}")
        except Exception as e:
            logger.error(f"Error saving strategy selection result: {e}")

    def load_advice_results(self, file_path: str) -> Dict[str, Any]:
        """加载建议结果 - 支持多种输入格式"""
        logger.info(f"Loading advice results from: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 检测数据格式并进行统一转换
        if self._is_strategy_input_format(data):
            logger.info("检测到策略输入格式，进行转换...")
            return self._convert_strategy_input_to_advice_format(data)
        elif self._is_cvss_format(data):
            logger.info("检测到CVSS格式，进行转换...")
            return self._convert_cvss_to_advice_format(data)
        else:
            logger.info("使用标准建议结果格式")
            return data
    
    def calculate_overall_security_level(self, advice_results: List[Dict[str, Any]]) -> Tuple[float, Dict[str, Any]]:
        """计算整体安全级别 - 实现论文中的 SLi = SiT*Wi = Σ(si_k * wi_k)"""
        
        logger.info("Calculating overall security level...")
        
        # 提取评分和权重
        scores = []
        weights = []
        agent_contributions = {}
        
        for result in advice_results:
            score = result.get('security_score', 0.0)
            weight = result.get('weight', 1.0)
            agent_id = result.get('agent_id', 'unknown')
            
            scores.append(score)
            weights.append(weight)
            agent_contributions[agent_id] = {
                'score': score,
                'weight': weight,
                'contribution': score * weight
            }
        
        # 计算加权平均 - 实现论文公式 SLi = Σ(si_k * wi_k)
        if not scores:
            return 0.0, {}
        
        numerator = sum(score * weight for score, weight in zip(scores, weights))
        denominator = sum(weights)
        
        overall_score = numerator / denominator if denominator > 0 else 0.0
        
        # 计算详细统计
        statistics = {
            'total_agents': len(advice_results),
            'average_score': sum(scores) / len(scores),
            'weighted_average': overall_score,
            'score_variance': np.var(scores),
            'weight_distribution': {
                'max_weight': max(weights),
                'min_weight': min(weights),
                'avg_weight': sum(weights) / len(weights)
            },
            'agent_contributions': agent_contributions
        }
        
        return overall_score, statistics
    
    def determine_threat_level(self, security_score: float) -> ThreatLevel:
        """确定威胁级别"""
        
        for (min_score, max_score), threat_level in self.threat_level_mapping.items():
            if min_score <= security_score < max_score:
                return threat_level
        
        return ThreatLevel.MINIMAL
    
    def generate_comprehensive_analysis(self, advice_results: List[Dict[str, Any]], 
                                      overall_score: float, 
                                      integrated_strategies: List[IntegratedStrategy]) -> str:
        """生成综合分析"""
        
        # 构建分析提示
        threat_summary = self._build_threat_summary(advice_results)
        strategy_summary = self._build_strategy_summary(integrated_strategies)
        
        analysis_prompt = f"""
You are a senior cybersecurity analyst responsible for providing comprehensive security assessments for Space-Air-Ground Integrated Networks (SAGIN).

Based on the multi-agent analysis results, provide a comprehensive security assessment and strategic recommendations.

=== SECURITY ASSESSMENT SUMMARY ===
Overall Security Score: {overall_score:.2f}/1.0
Total Analyzed Threats: {len(advice_results)}
Threat Level: {self.determine_threat_level(overall_score).value}

=== THREAT LANDSCAPE ===
{threat_summary}

=== INTEGRATED STRATEGIES ===
{strategy_summary}

=== ANALYSIS REQUIREMENTS ===
Please provide a comprehensive analysis including:

1. Executive Summary
   - Overall security posture assessment
   - Key findings and critical vulnerabilities
   - Immediate risk factors

2. Threat Assessment
   - Priority threats and attack vectors
   - Potential impact on SAGIN infrastructure
   - Risk correlation and cascading effects

3. Strategic Recommendations
   - Immediate actions (0-24 hours)
   - Short-term measures (1-7 days)
   - Long-term security enhancements (1-3 months)

4. Resource Requirements
   - Technical resources needed
   - Personnel and training requirements
   - Budget considerations and priorities

5. Implementation Roadmap
   - Phased implementation approach
   - Success metrics and KPIs
   - Risk mitigation during implementation

Provide a detailed, actionable analysis that security leadership can use for decision-making.

Comprehensive Security Analysis:
"""
        
        return self.llm.generate_response(analysis_prompt)
    
    def _build_threat_summary(self, advice_results: List[Dict[str, Any]]) -> str:
        """构建威胁摘要"""
        
        threat_categories = defaultdict(list)
        
        for result in advice_results:
            category = result.get('threat_category', 'UNKNOWN')
            score = result.get('security_score', 0.0)
            cvss_score = result.get('cvss_metrics', {}).get('base_score', 0.0)
            
            threat_categories[category].append({
                'score': score,
                'cvss': cvss_score,
                'agent_id': result.get('agent_id', 'unknown')
            })
        
        summary_parts = []
        
        for category, threats in threat_categories.items():
            avg_score = sum(t['score'] for t in threats) / len(threats)
            max_cvss = max(t['cvss'] for t in threats)
            
            summary_parts.append(
                f"- {category}: {len(threats)} instances, "
                f"Average Score: {avg_score:.2f}, "
                f"Max CVSS: {max_cvss:.1f}"
            )
        
        return "\n".join(summary_parts)
    
    def _build_strategy_summary(self, integrated_strategies: List[IntegratedStrategy]) -> str:
        """构建策略摘要"""
        
        if not integrated_strategies:
            return "No integrated strategies generated."
        
        summary_parts = []
        
        for strategy in integrated_strategies:
            summary_parts.append(
                f"- {strategy.priority.value}: {len(strategy.actions)} actions, "
                f"Effectiveness: {strategy.effectiveness_score:.2f}, "
                f"Complexity: {strategy.implementation_complexity}"
            )
        
        return "\n".join(summary_parts)
    
    def generate_action_plan(self, integrated_strategies: List[IntegratedStrategy]) -> List[Dict[str, Any]]:
        """生成行动计划"""
        
        action_plan = []
        
        for i, strategy in enumerate(integrated_strategies):
            for j, action in enumerate(strategy.actions):
                action_item = {
                    'action_id': f"action_{i+1}_{j+1}",
                    'strategy_id': strategy.strategy_id,
                    'priority': strategy.priority.value,
                    'action_description': action,
                    'timeline': strategy.timeline,
                    'resources_required': strategy.resources_required,
                    'complexity': strategy.implementation_complexity,
                    'dependencies': strategy.dependencies,
                    'effectiveness_score': strategy.effectiveness_score
                }
                action_plan.append(action_item)
        
        # 按优先级和效果评分排序
        action_plan.sort(key=lambda x: (
            self._get_priority_order(x['priority']),
            -x['effectiveness_score']
        ))
        
        return action_plan
    
    def _get_priority_order(self, priority: str) -> int:
        """获取优先级顺序"""
        priority_order = {
            'IMMEDIATE': 1,
            'SHORT_TERM': 2,
            'MEDIUM_TERM': 3,
            'LONG_TERM': 4
        }
        return priority_order.get(priority, 5)
    
    def generate_risk_assessment(self, advice_results: List[Dict[str, Any]]) -> Dict[str, float]:
        """生成风险评估"""
        
        risk_categories = {
            'confidentiality_risk': 0.0,
            'integrity_risk': 0.0,
            'availability_risk': 0.0,
            'operational_risk': 0.0,
            'compliance_risk': 0.0
        }
        
        for result in advice_results:
            cvss_metrics = result.get('cvss_metrics', {})
            weight = result.get('weight', 1.0)
            
            if isinstance(cvss_metrics, dict):
                # 提取CVSS影响指标
                conf_impact = cvss_metrics.get('confidentiality', 0.0)
                int_impact = cvss_metrics.get('integrity', 0.0)
                avail_impact = cvss_metrics.get('availability', 0.0)
                
                # 更新风险评估
                risk_categories['confidentiality_risk'] += conf_impact * weight
                risk_categories['integrity_risk'] += int_impact * weight
                risk_categories['availability_risk'] += avail_impact * weight
                
                # 基于威胁类型评估操作风险
                threat_category = result.get('threat_category', '')
                if threat_category in ['JAMMING', 'DENIAL_OF_SERVICE']:
                    risk_categories['operational_risk'] += 0.8 * weight
                elif threat_category in ['INSIDER_THREAT', 'MALWARE']:
                    risk_categories['compliance_risk'] += 0.7 * weight
        
        # 标准化风险评分
        max_weight = sum(result.get('weight', 1.0) for result in advice_results)
        if max_weight > 0:
            for key in risk_categories:
                risk_categories[key] = min(1.0, risk_categories[key] / max_weight)
        
        return risk_categories
    
    def generate_recommendations(self, integrated_strategies: List[IntegratedStrategy], 
                               threat_level: ThreatLevel) -> List[str]:
        """生成建议"""
        
        recommendations = []
        
        # 基于威胁级别的基础建议
        if threat_level == ThreatLevel.CRITICAL:
            recommendations.append("Activate incident response team immediately")
            recommendations.append("Implement emergency security measures")
            recommendations.append("Isolate affected systems and networks")
        elif threat_level == ThreatLevel.HIGH:
            recommendations.append("Escalate to security leadership")
            recommendations.append("Implement immediate containment measures")
            recommendations.append("Increase monitoring and alerting")
        elif threat_level == ThreatLevel.MEDIUM:
            recommendations.append("Schedule security review meeting")
            recommendations.append("Update security monitoring rules")
            recommendations.append("Plan preventive measures implementation")
        
        # 基于策略的建议
        for strategy in integrated_strategies[:3]:  # 前3个高优先级策略
            if strategy.priority == StrategyPriority.IMMEDIATE:
                recommendations.append(f"Execute {strategy.priority.value} strategy: {strategy.actions[0] if strategy.actions else 'No specific actions'}")
        
        # 通用建议
        recommendations.extend([
            "Conduct regular security assessments",
            "Update threat intelligence feeds",
            "Review and update security policies",
            "Provide security awareness training"
        ])
        
        return recommendations[:10]  # 限制建议数量
    
    def calculate_confidence_score(self, advice_results: List[Dict[str, Any]], 
                                  conflict_resolutions: List[ConflictResolution]) -> float:
        """计算置信度分数"""
        
        # 基于个体Agent置信度
        individual_confidences = [r.get('confidence', 0.0) for r in advice_results]
        avg_confidence = sum(individual_confidences) / len(individual_confidences) if individual_confidences else 0.0
        
        # 基于冲突解决的置信度
        conflict_confidence = 1.0
        if conflict_resolutions:
            resolution_confidences = [cr.confidence for cr in conflict_resolutions]
            conflict_confidence = sum(resolution_confidences) / len(resolution_confidences)
        
        # 基于一致性的置信度
        security_scores = [r.get('security_score', 0.0) for r in advice_results]
        if len(security_scores) > 1:
            score_variance = np.var(security_scores)
            consistency_confidence = max(0.0, 1.0 - score_variance)
        else:
            consistency_confidence = 1.0
        
        # 综合置信度
        overall_confidence = (
            avg_confidence * 0.4 +
            conflict_confidence * 0.3 +
            consistency_confidence * 0.3
        )
        
        return min(1.0, max(0.0, overall_confidence))
    
    def process_advice_results(self, advice_data: Dict[str, Any]) -> DecisionResult:
        """处理建议结果"""
        
        logger.info("Processing advice results with Comprehensive Decision Agent...")
        
        # 提取建议结果
        advice_results = advice_data.get('advice_results', [])
        
        if not advice_results:
            raise ValueError("No advice results found in input data")
        
        # 计算整体安全级别
        overall_score, statistics = self.calculate_overall_security_level(advice_results)
        
        # 确定威胁级别
        threat_level = self.determine_threat_level(overall_score)
        
        # 整合策略
        integrated_strategies = self.strategy_integrator.integrate_strategies(advice_results)
        
        # 解决冲突
        conflict_resolutions = self.conflict_resolver.resolve_conflicts(advice_results)
        
        # 生成综合分析
        detailed_analysis = self.generate_comprehensive_analysis(
            advice_results, overall_score, integrated_strategies
        )
        
        # 生成行动计划
        action_plan = self.generate_action_plan(integrated_strategies)
        
        # 生成风险评估
        risk_assessment = self.generate_risk_assessment(advice_results)
        
        # 生成建议
        recommendations = self.generate_recommendations(integrated_strategies, threat_level)
        
        # 计算置信度
        confidence_score = self.calculate_confidence_score(advice_results, conflict_resolutions)
        
        # 提取贡献Agent
        contributing_agents = [result.get('agent_id', 'unknown') for result in advice_results]
        
        # 选择主要整合策略
        main_strategy = integrated_strategies[0] if integrated_strategies else IntegratedStrategy(
            strategy_id="default",
            threat_categories=["GENERAL"],
            priority=StrategyPriority.MEDIUM_TERM,
            actions=["No specific actions available"],
            resources_required=["General resources"],
            timeline="To be determined",
            effectiveness_score=0.0,
            implementation_complexity="UNKNOWN",
            dependencies=[]
        )
        
        # 生成决策ID
        decision_id = hashlib.md5(f"{datetime.now().isoformat()}_{overall_score}".encode()).hexdigest()[:12]
        
        # 创建决策结果
        decision_result = DecisionResult(
            decision_id=decision_id,
            overall_security_level=overall_score,
            threat_level=threat_level,
            integrated_strategy=main_strategy,
            detailed_analysis=detailed_analysis,
            confidence_score=confidence_score,
            contributing_agents=contributing_agents,
            risk_assessment=risk_assessment,
            recommendations=recommendations,
            action_plan=action_plan,
            created_at=datetime.now().isoformat()
        )
        
        return decision_result
    
    def save_results(self, decision_result: DecisionResult, 
                    advice_data: Dict[str, Any], 
                    filename: str = None) -> str:
        """保存结果"""
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comprehensive_decision_results_{timestamp}.json"
        
        output_path = self.output_dir / filename
        
        # 构建完整结果
        full_results = {
            'decision_result': self._serialize_decision_result(decision_result),
            'input_summary': {
                'total_agents': len(advice_data.get('advice_results', [])),
                'agent_weights': advice_data.get('agent_weights', {}),
                'processing_time': advice_data.get('processing_time', ''),
                'model_used': advice_data.get('model_used', '')
            },
            'metadata': {
                'comprehensive_agent_config': {
                    'embedding_model': self.config.embedding_model,
                    'decision_threshold': self.config.decision_threshold,
                    'conflict_resolution_method': self.config.conflict_resolution_method,
                    'max_strategies': self.config.max_strategies
                },
                'processing_timestamp': datetime.now().isoformat(),
                'version': "1.0"
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(full_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Results saved to: {output_path}")
        return str(output_path)

    def _serialize_decision_result(self, decision_result) -> Dict[str, Any]:
        """序列化决策结果，处理Enum类型"""

        from dataclasses import asdict
        from enum import Enum

        def serialize_value(obj):
            if isinstance(obj, Enum):
                return obj.value
            elif hasattr(obj, '__dict__'):
                return {k: serialize_value(v) for k, v in obj.__dict__.items()}
            elif isinstance(obj, list):
                return [serialize_value(item) for item in obj]
            elif isinstance(obj, dict):
                return {k: serialize_value(v) for k, v in obj.items()}
            else:
                return obj

        # 转换为字典
        result_dict = asdict(decision_result)

        # 递归序列化所有值
        return serialize_value(result_dict)

    def _is_strategy_input_format(self, data: Any) -> bool:
        """检测是否为策略输入格式（包含instruction和input字段，不含output字段）"""

        # 检查是否为转换器输出的格式
        if isinstance(data, dict) and "data" in data and "conversion_metadata" in data:
            data = data["data"]

        if isinstance(data, list) and len(data) > 0:
            first_item = data[0]
            return (isinstance(first_item, dict) and
                   "instruction" in first_item and
                   "input" in first_item and
                   "output" not in first_item)

        return False

    def _is_cvss_format(self, data: Any) -> bool:
        """检测是否为CVSS格式"""

        if isinstance(data, list):
            # 检查是否为嵌套的CVSS格式
            def check_cvss_recursive(item):
                if isinstance(item, list):
                    return any(check_cvss_recursive(sub_item) for sub_item in item)
                elif isinstance(item, dict):
                    return ("CVE ID" in item and "Base Score" in item and "Description" in item)
                return False

            return check_cvss_recursive(data)

        return False

    def _convert_strategy_input_to_advice_format(self, strategy_input_data: Any) -> Dict[str, Any]:
        """将策略输入格式转换为建议结果格式"""

        # 提取数据部分
        if isinstance(strategy_input_data, dict) and "data" in strategy_input_data:
            data_list = strategy_input_data["data"]
            metadata = strategy_input_data.get("conversion_metadata", {})
        else:
            data_list = strategy_input_data
            metadata = {}

        advice_results = []

        for item in data_list:
            input_text = item.get("input", "")

            # 从input文本中提取信息
            threat_info = self._parse_strategy_input_text(input_text)

            # 转换为建议结果格式
            advice_item = {
                "Description": threat_info.get("description", input_text),
                "Base Score": threat_info.get("cvss_score", 0.0),
                "Threat Type": threat_info.get("threat_type", "Unknown"),
                "Advice": f"Strategy selection required for {threat_info.get('threat_type', 'threat')}",
                "Processing Time": datetime.now().isoformat(),
                "agent_id": "strategy_input_converter",
                "cvss_metrics": {
                    "base_score": threat_info.get("cvss_score", 0.0)
                },
                "strategy_instruction": item.get("instruction", ""),
                "original_input": input_text
            }

            advice_results.append(advice_item)

        return {
            "advice_results": advice_results,
            "agent_weights": {"strategy_input_converter": 1.0},
            "processing_time": datetime.now().isoformat(),
            "model_used": "strategy_input_converter",
            "conversion_metadata": metadata
        }

    def _parse_strategy_input_text(self, input_text: str) -> Dict[str, Any]:
        """解析策略输入文本，提取威胁信息"""

        import re

        threat_info = {
            "description": input_text,
            "threat_type": "Unknown",
            "cvss_score": 0.0,
            "ip_address": "Unknown",
            "attack_method": "Unknown",
            "impact": "Unknown",
            "vulnerability": "Unknown"
        }

        # 提取IP地址
        ip_match = re.search(r'IP:\s*\[([^\]]+)\]', input_text)
        if ip_match:
            threat_info["ip_address"] = ip_match.group(1)

        # 提取威胁类型
        threat_patterns = [
            (r'DDoS|ddos|denial of service', 'DDoS'),
            (r'infiltration|brute force|unauthorized', 'Infiltration'),
            (r'eavesdrop|intercept|unencrypted', 'Eavesdropping'),
            (r'port scan|scanning', 'Port Scan'),
            (r'sql injection|sql', 'SQL Injection'),
            (r'arp spoofing|arp', 'ARP Spoofing')
        ]

        for pattern, threat_type in threat_patterns:
            if re.search(pattern, input_text, re.IGNORECASE):
                threat_info["threat_type"] = threat_type
                break

        # 提取CVSS评分
        cvss_match = re.search(r'CVSS Score:\s*([0-9.]+)', input_text)
        if cvss_match:
            threat_info["cvss_score"] = float(cvss_match.group(1))

        # 提取攻击方法
        attack_match = re.search(r'Attack Method:\s*([^.\\n]+)', input_text)
        if attack_match:
            threat_info["attack_method"] = attack_match.group(1).strip()

        # 提取影响
        impact_match = re.search(r'Impact:\s*([^.\\n]+)', input_text)
        if impact_match:
            threat_info["impact"] = impact_match.group(1).strip()

        # 提取漏洞
        vuln_match = re.search(r'Vulnerability:\s*([^.\\n]+)', input_text)
        if vuln_match:
            threat_info["vulnerability"] = vuln_match.group(1).strip()

        return threat_info

    def _convert_cvss_to_advice_format(self, cvss_data: Any) -> Dict[str, Any]:
        """将CVSS格式转换为建议结果格式"""

        # 展平CVSS数据
        flattened_data = self._flatten_cvss_data(cvss_data)

        advice_results = []

        for item in flattened_data:
            advice_item = {
                "Description": item.get("Description", ""),
                "Base Score": item.get("Base Score", 0.0),
                "Threat Type": self._classify_threat_from_cvss_description(item.get("Description", "")),
                "Advice": f"CVSS-based threat analysis required",
                "Processing Time": datetime.now().isoformat(),
                "agent_id": "cvss_converter",
                "CVE ID": item.get("CVE ID", ""),
                "Time": item.get("Time", ""),
                "Metrics": item.get("Metrics", []),
                "cvss_metrics": {
                    "base_score": item.get("Base Score", 0.0),
                    "metrics": item.get("Metrics", [])
                }
            }

            advice_results.append(advice_item)

        return {
            "advice_results": advice_results,
            "agent_weights": {"cvss_converter": 1.0},
            "processing_time": datetime.now().isoformat(),
            "model_used": "cvss_converter"
        }

    def _flatten_cvss_data(self, cvss_data: Any) -> List[Dict[str, Any]]:
        """展平嵌套的CVSS数据结构"""

        flattened = []

        def flatten_recursive(data):
            if isinstance(data, list):
                for item in data:
                    flatten_recursive(item)
            elif isinstance(data, dict):
                if "Description" in data and "Base Score" in data:
                    flattened.append(data)
                else:
                    for value in data.values():
                        flatten_recursive(value)

        flatten_recursive(cvss_data)
        return flattened

    def _classify_threat_from_cvss_description(self, description: str) -> str:
        """从CVSS描述中分类威胁类型"""

        description_lower = description.lower()

        threat_keywords = {
            "DDoS": ["ddos", "denial of service", "flood", "loit"],
            "Infiltration": ["brute force", "login", "ssh", "unauthorized"],
            "Port Scan": ["port scan", "nmap", "scanning"],
            "Eavesdropping": ["eavesdrop", "intercept", "unencrypted"],
            "ARP Spoofing": ["arp", "spoofing", "poisoning"],
            "SQL Injection": ["sql", "injection"],
            "Malware": ["malware", "virus", "trojan"]
        }

        for threat_type, keywords in threat_keywords.items():
            for keyword in keywords:
                if keyword in description_lower:
                    return threat_type

        return "Unknown"

    def run(self, input_path: str, output_filename: str = None) -> str:
        """运行Comprehensive Decision Agent"""
        
        logger.info("Starting Comprehensive Decision Agent processing...")
        
        # 加载建议结果
        advice_data = self.load_advice_results(input_path)
        
        # 处理建议结果
        decision_result = self.process_advice_results(advice_data)
        
        # 保存结果
        output_path = self.save_results(decision_result, advice_data, output_filename)
        
        logger.info("Comprehensive Decision Agent processing completed!")
        
        # 输出摘要
        self._print_decision_summary(decision_result)
        
        return output_path
    
    def _print_decision_summary(self, decision_result: DecisionResult):
        """打印决策摘要"""
        
        print("\n" + "="*60)
        print("COMPREHENSIVE SECURITY DECISION SUMMARY")
        print("="*60)
        print(f"Decision ID: {decision_result.decision_id}")
        print(f"Overall Security Level: {decision_result.overall_security_level:.2f}/1.0")
        print(f"Threat Level: {decision_result.threat_level.value}")
        print(f"Confidence Score: {decision_result.confidence_score:.2f}")
        print(f"Contributing Agents: {len(decision_result.contributing_agents)}")
        
        print(f"\nMain Strategy: {decision_result.integrated_strategy.priority.value}")
        print(f"Actions: {len(decision_result.integrated_strategy.actions)}")
        print(f"Timeline: {decision_result.integrated_strategy.timeline}")
        
        print(f"\nTop Recommendations:")
        for i, rec in enumerate(decision_result.recommendations[:5], 1):
            print(f"{i}. {rec}")
        
        print(f"\nRisk Assessment:")
        for risk_type, score in decision_result.risk_assessment.items():
            print(f"- {risk_type.replace('_', ' ').title()}: {score:.2f}")
        
        print("="*60)

# 示例用法
def main():
    """主函数示例"""
    
    # 配置参数
    config = ComprehensiveDecisionConfig(
        model_type="llama3-8b",  # 可选: llama3-8b, llama3-70b, chatgpt-3.5, chatgpt-4
        model_path="meta-llama/Llama-3.1-8B-Instruct",
        embedding_model="all-MiniLM-L6-v2",
        vector_db_path="./vector_db",
        temperature=0.7,
        output_dir="./comprehensive_decision_outputs",
        quantization=True,
        decision_threshold=0.5,
        conflict_resolution_method="weighted_average",
        enable_explanation=True
    )
    
    # 初始化Agent
    agent = ComprehensiveDecisionAgent(config)
    
    # 处理建议结果
    input_path = "path/to/specific_advice_results.json"  # 替换为实际路径
    output_path = agent.run(input_path)
    
    print(f"Processing completed! Results saved to: {output_path}")

if __name__ == "__main__":
    main()