#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一工作流程配置
定义统一数据格式和工作流程的配置参数
"""

from dataclasses import dataclass
from typing import Dict, List, Any, Optional
from pathlib import Path

@dataclass
class UnifiedWorkflowConfig:
    """统一工作流程配置"""
    
    # 数据转换配置
    enable_auto_format_detection: bool = True
    enable_format_conversion: bool = True
    
    # 输入文件路径配置
    specific_advice_output_path: str = "specific_advice_outputs"
    cvss_data_file: str = "output_0525_finetune_metrics.json"
    strategy_training_data_file: str = "output_1112_strategy_train_data.json"
    
    # 输出路径配置
    unified_data_output_dir: str = "unified_workflow_outputs"
    conversion_log_file: str = "unified_workflow_outputs/conversion.log"
    
    # Comprehensive Decision Agent配置
    comprehensive_agent_config: Dict[str, Any] = None
    
    # 支持的输入格式
    supported_input_formats: List[str] = None
    
    # 格式检测规则
    format_detection_rules: Dict[str, Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        
        if self.comprehensive_agent_config is None:
            self.comprehensive_agent_config = {
                "output_dir": self.unified_data_output_dir,
                "max_strategies": 10,
                "enable_explanation": True,
                "decision_threshold": 0.5,
                "conflict_resolution_method": "weighted_average",
                "temperature": 0.7,
                "quantization": True
            }
        
        if self.supported_input_formats is None:
            self.supported_input_formats = [
                "specific_advice_output",
                "cvss_data",
                "strategy_input",
                "unified_format"
            ]
        
        if self.format_detection_rules is None:
            self.format_detection_rules = {
                "specific_advice_output": {
                    "required_fields": ["Description", "Base Score", "Threat Type", "Advice"],
                    "optional_fields": ["Processing Time", "Metrics"],
                    "structure": "list_of_dicts"
                },
                "cvss_data": {
                    "required_fields": ["CVE ID", "Base Score", "Description"],
                    "optional_fields": ["Time", "Metrics"],
                    "structure": "nested_list"
                },
                "strategy_input": {
                    "required_fields": ["instruction", "input"],
                    "forbidden_fields": ["output"],
                    "structure": "list_of_dicts"
                },
                "unified_format": {
                    "required_fields": ["data", "conversion_metadata"],
                    "data_structure": "converted_format",
                    "structure": "wrapper_dict"
                }
            }

class UnifiedWorkflowManager:
    """统一工作流程管理器"""
    
    def __init__(self, config: UnifiedWorkflowConfig = None):
        """初始化管理器"""
        
        self.config = config or UnifiedWorkflowConfig()
        
        # 确保输出目录存在
        Path(self.config.unified_data_output_dir).mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self._init_components()
    
    def _init_components(self):
        """初始化组件"""
        
        # 导入必要的模块
        from data_converter import UnifiedDataConverter
        
        # 初始化数据转换器
        self.converter = UnifiedDataConverter()
        
        # 初始化Comprehensive Decision Agent
        self._init_comprehensive_agent()
    
    def _init_comprehensive_agent(self):
        """初始化Comprehensive Decision Agent"""
        
        try:
            from Comprehensive_Decision_Agent import ComprehensiveDecisionAgent, ComprehensiveDecisionConfig
        except ImportError:
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                "comprehensive_decision_agent", 
                "Comprehensive Decision Agent.py"
            )
            comprehensive_decision_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(comprehensive_decision_module)
            ComprehensiveDecisionAgent = comprehensive_decision_module.ComprehensiveDecisionAgent
            ComprehensiveDecisionConfig = comprehensive_decision_module.ComprehensiveDecisionConfig
        
        # 创建配置
        agent_config = ComprehensiveDecisionConfig(**self.config.comprehensive_agent_config)
        
        # 创建Agent实例
        self.comprehensive_agent = ComprehensiveDecisionAgent(agent_config)
    
    def detect_input_format(self, input_path: str) -> str:
        """检测输入文件格式"""
        
        import json
        
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检测各种格式
            for format_name, rules in self.config.format_detection_rules.items():
                if self._check_format_rules(data, rules):
                    return format_name
            
            return "unknown"
            
        except Exception as e:
            print(f"格式检测失败: {e}")
            return "error"
    
    def _check_format_rules(self, data: Any, rules: Dict[str, Any]) -> bool:
        """检查数据是否符合格式规则"""
        
        structure = rules.get("structure", "")
        required_fields = rules.get("required_fields", [])
        optional_fields = rules.get("optional_fields", [])
        forbidden_fields = rules.get("forbidden_fields", [])
        
        if structure == "list_of_dicts":
            if not isinstance(data, list) or len(data) == 0:
                return False
            
            first_item = data[0]
            if not isinstance(first_item, dict):
                return False
            
            # 检查必需字段
            for field in required_fields:
                if field not in first_item:
                    return False
            
            # 检查禁止字段
            for field in forbidden_fields:
                if field in first_item:
                    return False
            
            return True
        
        elif structure == "nested_list":
            # 检查嵌套列表结构（CVSS格式）
            def check_nested(item):
                if isinstance(item, list):
                    return any(check_nested(sub_item) for sub_item in item)
                elif isinstance(item, dict):
                    return all(field in item for field in required_fields)
                return False
            
            return check_nested(data)
        
        elif structure == "wrapper_dict":
            if not isinstance(data, dict):
                return False
            
            return all(field in data for field in required_fields)
        
        return False
    
    def process_input(self, input_path: str, output_filename: str = None) -> str:
        """处理输入文件，自动检测格式并转换"""
        
        print(f"🔍 检测输入文件格式: {input_path}")
        
        # 检测格式
        input_format = self.detect_input_format(input_path)
        print(f"   检测到格式: {input_format}")
        
        if input_format == "unknown":
            print("   ⚠️  未知格式，尝试直接处理...")
            return self.comprehensive_agent.run(input_path, output_filename)
        
        elif input_format == "unified_format":
            print("   ✅ 已是统一格式，直接处理...")
            return self.comprehensive_agent.run(input_path, output_filename)
        
        else:
            print(f"   🔄 转换 {input_format} 格式为统一格式...")
            
            # 转换为统一格式
            unified_data = self._convert_to_unified_format(input_path, input_format)
            
            # 保存统一格式数据
            unified_file_path = self._save_unified_data(unified_data, input_format)
            
            print(f"   ✅ 统一格式数据已保存: {unified_file_path}")
            
            # 使用Comprehensive Decision Agent处理
            print("   🚀 开始策略选择处理...")
            return self.comprehensive_agent.run(unified_file_path, output_filename)
    
    def _convert_to_unified_format(self, input_path: str, input_format: str) -> List[Dict[str, str]]:
        """根据格式转换为统一格式"""
        
        if input_format == "specific_advice_output":
            return self.converter.convert_specific_advice_to_strategy_input(input_path)
        
        elif input_format == "cvss_data":
            return self.converter.convert_cvss_data_to_strategy_input(input_path)
        
        elif input_format == "strategy_input":
            # 已经是策略输入格式，直接读取
            import json
            with open(input_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data if isinstance(data, list) else data.get("data", [])
        
        else:
            raise ValueError(f"不支持的输入格式: {input_format}")
    
    def _save_unified_data(self, unified_data: List[Dict[str, str]], source_format: str) -> str:
        """保存统一格式数据"""
        
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"unified_data_from_{source_format}_{timestamp}.json"
        file_path = Path(self.config.unified_data_output_dir) / filename
        
        return self.converter.save_converted_data(unified_data, str(file_path))
    
    def run_collaborative_workflow(self, specific_advice_output_path: str) -> str:
        """运行协作工作流程"""
        
        print("🤝 运行协作工作流程...")
        return self.process_input(specific_advice_output_path, "collaborative_workflow_result.json")
    
    def run_standalone_workflow(self, input_path: str = None) -> str:
        """运行单独工作流程"""
        
        print("🔧 运行单独工作流程...")
        
        # 如果没有指定输入路径，尝试使用默认的CVSS数据文件
        if input_path is None:
            if Path(self.config.cvss_data_file).exists():
                input_path = self.config.cvss_data_file
            elif Path(self.config.strategy_training_data_file).exists():
                input_path = self.config.strategy_training_data_file
            else:
                raise FileNotFoundError("没有找到可用的输入数据文件")
        
        return self.process_input(input_path, "standalone_workflow_result.json")
    
    def get_workflow_summary(self) -> Dict[str, Any]:
        """获取工作流程摘要"""
        
        output_dir = Path(self.config.unified_data_output_dir)
        
        summary = {
            "config": {
                "supported_formats": self.config.supported_input_formats,
                "output_directory": str(output_dir),
                "auto_detection_enabled": self.config.enable_auto_format_detection
            },
            "output_files": [],
            "statistics": {
                "total_files": 0,
                "total_size_bytes": 0
            }
        }
        
        if output_dir.exists():
            for file_path in output_dir.glob("*.json"):
                file_info = {
                    "name": file_path.name,
                    "size_bytes": file_path.stat().st_size,
                    "modified_time": file_path.stat().st_mtime
                }
                summary["output_files"].append(file_info)
                summary["statistics"]["total_files"] += 1
                summary["statistics"]["total_size_bytes"] += file_info["size_bytes"]
        
        return summary

# 便捷函数
def create_unified_workflow(config: UnifiedWorkflowConfig = None) -> UnifiedWorkflowManager:
    """创建统一工作流程管理器"""
    return UnifiedWorkflowManager(config)

def quick_process(input_path: str, output_filename: str = None) -> str:
    """快速处理输入文件"""
    manager = create_unified_workflow()
    return manager.process_input(input_path, output_filename)
